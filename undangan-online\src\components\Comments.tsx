'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import Image from 'next/image';

interface Comment {
  id: string;
  name: string;
  message: string;
  timestamp: Date;
  avatar?: string;
}

export default function Comments() {
  const [comments, setComments] = useState<Comment[]>([]);
  const [newComment, setNewComment] = useState({ name: '', message: '' });
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Load comments from localStorage on component mount
  useEffect(() => {
    const savedComments = localStorage.getItem('wedding-comments');
    if (savedComments) {
      const parsedComments = JSON.parse(savedComments).map((comment: Comment & { timestamp: string }) => ({
        ...comment,
        timestamp: new Date(comment.timestamp)
      }));
      setComments(parsedComments);
    }
  }, []);

  // Save comments to localStorage whenever comments change
  useEffect(() => {
    if (comments.length > 0) {
      localStorage.setItem('wedding-comments', JSON.stringify(comments));
    }
  }, [comments]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!newComment.name.trim() || !newComment.message.trim()) return;

    setIsSubmitting(true);

    const comment: Comment = {
      id: Date.now().toString(),
      name: newComment.name.trim(),
      message: newComment.message.trim(),
      timestamp: new Date(),
      avatar: `https://ui-avatars.com/api/?name=${encodeURIComponent(newComment.name)}&background=ec4899&color=fff&size=40`
    };

    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1000));

    setComments(prev => [comment, ...prev]);
    setNewComment({ name: '', message: '' });
    setIsSubmitting(false);
  };

  const formatTimeAgo = (date: Date) => {
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 1) return 'Baru saja';
    if (diffInMinutes < 60) return `${diffInMinutes} menit yang lalu`;
    
    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) return `${diffInHours} jam yang lalu`;
    
    const diffInDays = Math.floor(diffInHours / 24);
    return `${diffInDays} hari yang lalu`;
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { y: 50, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.8
      }
    }
  };

  return (
    <section className="py-20 bg-cream">
      <div className="container mx-auto px-4 max-w-4xl">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
        >
          {/* Header */}
          <motion.div variants={itemVariants} className="text-center mb-12">
            <h2 className="text-4xl font-bold ornament-gold mb-4">
              💌 Pesan & Doa
            </h2>
            <div className="w-24 h-1 bg-gold-gradient mx-auto mb-4"></div>
            <div className="text-2xl text-gold mb-4">❋ ✦ ❋</div>
            <p className="text-dark-brown max-w-2xl mx-auto">
              Tinggalkan pesan dan doa terbaik untuk kami. Setiap kata dari Anda sangat berarti bagi kami.
            </p>
          </motion.div>

          {/* Comment Form */}
          <motion.div variants={itemVariants} className="bg-cream-light rounded-2xl shadow-gold p-6 mb-8 border-2 border-gold">
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="grid md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-dark-brown mb-2">
                    Nama Anda
                  </label>
                  <input
                    type="text"
                    value={newComment.name}
                    onChange={(e) => setNewComment(prev => ({ ...prev, name: e.target.value }))}
                    className="w-full px-4 py-3 border border-gold rounded-lg focus:ring-2 focus:ring-gold focus:border-transparent transition-all duration-300 bg-cream"
                    placeholder="Masukkan nama Anda"
                    required
                  />
                </div>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-dark-brown mb-2">
                  Pesan & Doa
                </label>
                <textarea
                  value={newComment.message}
                  onChange={(e) => setNewComment(prev => ({ ...prev, message: e.target.value }))}
                  rows={4}
                  className="w-full px-4 py-3 border border-gold rounded-lg focus:ring-2 focus:ring-gold focus:border-transparent transition-all duration-300 resize-none bg-cream"
                  placeholder="Tulis pesan dan doa terbaik untuk kami..."
                  required
                />
              </div>

              <motion.button
                type="submit"
                disabled={isSubmitting}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                className="w-full bg-gradient-to-r from-pink-500 to-rose-500 text-white py-3 px-6 rounded-lg font-semibold shadow-lg hover:shadow-xl transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isSubmitting ? (
                  <div className="flex items-center justify-center gap-2">
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                    Mengirim...
                  </div>
                ) : (
                  'Kirim Pesan'
                )}
              </motion.button>
            </form>
          </motion.div>

          {/* Comments List */}
          <motion.div variants={itemVariants} className="space-y-4">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-xl font-semibold text-gray-800">
                Pesan dari Tamu ({comments.length})
              </h3>
            </div>

            <AnimatePresence>
              {comments.length === 0 ? (
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  className="text-center py-12 text-gray-500"
                >
                  <div className="text-4xl mb-4">💭</div>
                  <p>Belum ada pesan. Jadilah yang pertama memberikan doa!</p>
                </motion.div>
              ) : (
                comments.map((comment, index) => (
                  <motion.div
                    key={comment.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    transition={{ delay: index * 0.1 }}
                    className="bg-white rounded-xl shadow-md p-6 hover:shadow-lg transition-shadow duration-300"
                  >
                    <div className="flex items-start gap-4">
                      <Image
                        src={comment.avatar || ''}
                        alt={comment.name}
                        width={40}
                        height={40}
                        className="w-10 h-10 rounded-full"
                      />
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-2">
                          <h4 className="font-semibold text-gray-800">{comment.name}</h4>
                          <span className="text-sm text-gray-500">
                            {formatTimeAgo(comment.timestamp)}
                          </span>
                        </div>
                        <p className="text-gray-700 leading-relaxed">{comment.message}</p>
                      </div>
                    </div>
                  </motion.div>
                ))
              )}
            </AnimatePresence>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
}
