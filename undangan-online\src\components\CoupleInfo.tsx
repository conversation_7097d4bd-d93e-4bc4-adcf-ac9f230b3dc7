'use client';

import { motion } from 'framer-motion';

export default function CoupleInfo() {
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.3
      }
    }
  };

  const itemVariants = {
    hidden: { y: 50, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.8
      }
    }
  };

  return (
    <section id="couple-info" className="py-20 px-4 bg-cream-light">
      <div className="max-w-6xl mx-auto">
        <motion.div
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.3 }}
          variants={containerVariants}
        >
          <motion.h2
            variants={itemVariants}
            className="text-4xl md:text-5xl font-bold text-center ornament-gold mb-8"
          >
            Mempelai
          </motion.h2>
          <motion.div
            variants={itemVariants}
            className="text-center mb-16"
          >
            <div className="w-24 h-1 bg-gold-gradient mx-auto mb-4"></div>
            <div className="text-2xl text-gold">❋ ✦ ❋</div>
          </motion.div>

          <div className="grid md:grid-cols-2 gap-12 items-center">
            {/* Bride */}
            <motion.div 
              variants={itemVariants}
              className="text-center"
            >
              <div className="relative mb-8">
                <motion.div
                  whileHover={{ scale: 1.05 }}
                  className="w-64 h-64 mx-auto bg-gold-gradient rounded-full flex items-center justify-center shadow-gold border-4 border-gold"
                >
                  <div className="text-8xl">👰🏻</div>
                </motion.div>
                <motion.div
                  animate={{ rotate: 360 }}
                  transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
                  className="absolute -top-4 -right-4 w-16 h-16 bg-gold rounded-full flex items-center justify-center text-2xl border-2 border-dark-gold"
                >
                  🌸
                </motion.div>
              </div>
              
              <h3 className="text-3xl font-bold ornament-gold mb-4">Sarah Putri</h3>
              <p className="text-dark-brown mb-4">
                Putri pertama dari
              </p>
              <p className="text-lg font-semibold text-gold mb-2">
                Bapak Suharto & Ibu Siti Aminah
              </p>
              <p className="text-dark-brown text-sm">
                Jakarta, Indonesia
              </p>
            </motion.div>

            {/* Heart Separator */}
            <motion.div 
              variants={itemVariants}
              className="flex justify-center md:hidden"
            >
              <motion.div
                animate={{ 
                  scale: [1, 1.2, 1],
                  rotate: [0, 5, -5, 0]
                }}
                transition={{ 
                  duration: 2, 
                  repeat: Infinity,
                  ease: "easeInOut"
                }}
                className="text-6xl text-gold"
              >
                💍
              </motion.div>
            </motion.div>

            {/* Groom */}
            <motion.div 
              variants={itemVariants}
              className="text-center"
            >
              <div className="relative mb-8">
                <motion.div
                  whileHover={{ scale: 1.05 }}
                  className="w-64 h-64 mx-auto bg-gold-gradient rounded-full flex items-center justify-center shadow-gold border-4 border-gold"
                >
                  <div className="text-8xl">🤵🏻</div>
                </motion.div>
                <motion.div
                  animate={{ rotate: -360 }}
                  transition={{ duration: 25, repeat: Infinity, ease: "linear" }}
                  className="absolute -top-4 -left-4 w-16 h-16 bg-gold rounded-full flex items-center justify-center text-2xl border-2 border-dark-gold"
                >
                  ⭐
                </motion.div>
              </div>
              
              <h3 className="text-3xl font-bold ornament-gold mb-4">Ahmad Rizki</h3>
              <p className="text-dark-brown mb-4">
                Putra kedua dari
              </p>
              <p className="text-lg font-semibold text-gold mb-2">
                Bapak Ahmad Yusuf & Ibu Fatimah
              </p>
              <p className="text-dark-brown text-sm">
                Bandung, Indonesia
              </p>
            </motion.div>
          </div>

          {/* Heart Separator for Desktop */}
          <motion.div 
            variants={itemVariants}
            className="hidden md:flex justify-center mt-16"
          >
            <motion.div
              animate={{ 
                scale: [1, 1.3, 1],
                rotate: [0, 10, -10, 0]
              }}
              transition={{ 
                duration: 3, 
                repeat: Infinity,
                ease: "easeInOut"
              }}
              className="text-8xl"
            >
              💕
            </motion.div>
          </motion.div>

          {/* Love Story Quote */}
          <motion.div 
            variants={itemVariants}
            className="text-center mt-16 max-w-2xl mx-auto"
          >
            <blockquote className="text-xl md:text-2xl text-gray-600 italic mb-4">
              &ldquo;Dan di antara tanda-tanda kekuasaan-Nya ialah Dia menciptakan untukmu
              isteri-isteri dari jenismu sendiri, supaya kamu cenderung dan merasa
              tenteram kepadanya, dan dijadikan-Nya diantaramu rasa kasih dan sayang.&rdquo;
            </blockquote>
            <cite className="text-gray-500 font-semibold">
              - QS. Ar-Rum: 21
            </cite>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
}
