'use client';

import { motion } from 'framer-motion';
import { useState } from 'react';

export default function Gallery() {
  const [selectedImage, setSelectedImage] = useState<number | null>(null);

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { scale: 0.8, opacity: 0 },
    visible: {
      scale: 1,
      opacity: 1,
      transition: {
        duration: 0.6
      }
    }
  };

  // Placeholder images with emojis for demonstration
  const galleryImages = [
    { id: 1, emoji: '💑', title: 'Foto Prewedding 1' },
    { id: 2, emoji: '👫', title: 'Foto Prewedding 2' },
    { id: 3, emoji: '💕', title: 'Foto Prewedding 3' },
    { id: 4, emoji: '🌹', title: 'Foto Prewedding 4' },
    { id: 5, emoji: '💖', title: 'Foto Prewedding 5' },
    { id: 6, emoji: '🎊', title: 'Foto Prewedding 6' },
  ];

  return (
    <section className="py-20 px-4 bg-cream-light">
      <div className="max-w-6xl mx-auto">
        <motion.div
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.3 }}
          variants={containerVariants}
        >
          <motion.h2
            variants={itemVariants}
            className="text-4xl md:text-5xl font-bold text-center ornament-gold mb-8"
          >
            Galeri Foto
          </motion.h2>

          <motion.div
            variants={itemVariants}
            className="text-center mb-8"
          >
            <div className="w-24 h-1 bg-gold-gradient mx-auto mb-4"></div>
            <div className="text-2xl text-gold">❋ ✦ ❋</div>
          </motion.div>

          <motion.p
            variants={itemVariants}
            className="text-center text-dark-brown mb-12 max-w-2xl mx-auto"
          >
            Beberapa momen indah perjalanan cinta kami yang ingin kami bagikan dengan Anda
          </motion.p>

          <motion.div 
            variants={containerVariants}
            className="grid grid-cols-2 md:grid-cols-3 gap-4"
          >
            {galleryImages.map((image, index) => (
              <motion.div
                key={image.id}
                variants={itemVariants}
                whileHover={{ scale: 1.05, rotate: index % 2 === 0 ? 2 : -2 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => setSelectedImage(index)}
                className="relative aspect-square bg-gold-gradient rounded-2xl shadow-gold cursor-pointer overflow-hidden group border-4 border-gold"
              >
                <div className="absolute inset-0 flex items-center justify-center">
                  <motion.div
                    animate={{ 
                      scale: [1, 1.2, 1],
                      rotate: [0, 5, -5, 0]
                    }}
                    transition={{ 
                      duration: 3, 
                      repeat: Infinity,
                      ease: "easeInOut",
                      delay: index * 0.2
                    }}
                    className="text-6xl md:text-8xl"
                  >
                    {image.emoji}
                  </motion.div>
                </div>
                
                <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300 flex items-end">
                  <div className="p-4 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                    <p className="font-semibold">{image.title}</p>
                  </div>
                </div>
              </motion.div>
            ))}
          </motion.div>

          {/* Modal for selected image */}
          {selectedImage !== null && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 bg-black bg-opacity-80 flex items-center justify-center z-50 p-4"
              onClick={() => setSelectedImage(null)}
            >
              <motion.div
                initial={{ scale: 0.8, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                exit={{ scale: 0.8, opacity: 0 }}
                className="bg-white rounded-3xl p-8 max-w-md w-full text-center"
                onClick={(e) => e.stopPropagation()}
              >
                <div className="text-8xl mb-4">
                  {galleryImages[selectedImage].emoji}
                </div>
                <h3 className="text-2xl font-bold text-gray-800 mb-4">
                  {galleryImages[selectedImage].title}
                </h3>
                <p className="text-gray-600 mb-6">
                  Momen indah dalam perjalanan cinta kami
                </p>
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={() => setSelectedImage(null)}
                  className="bg-gradient-to-r from-rose-400 to-pink-500 text-white px-6 py-2 rounded-full font-semibold"
                >
                  Tutup
                </motion.button>
              </motion.div>
            </motion.div>
          )}

          {/* Love Quote */}
          <motion.div 
            variants={itemVariants}
            className="text-center mt-16 max-w-2xl mx-auto"
          >
            <motion.div
              animate={{ 
                rotate: [0, 5, -5, 0],
                scale: [1, 1.1, 1]
              }}
              transition={{ 
                duration: 4, 
                repeat: Infinity,
                ease: "easeInOut"
              }}
              className="text-6xl mb-6"
            >
              💝
            </motion.div>
            <blockquote className="text-xl text-gray-600 italic mb-4">
              &ldquo;Cinta sejati bukan tentang menjadi tak terpisahkan;
              itu tentang menjadi terpisah dan tidak ada yang berubah.&rdquo;
            </blockquote>
            <cite className="text-gray-500 font-semibold">
              - Sarah & Ahmad
            </cite>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
}
