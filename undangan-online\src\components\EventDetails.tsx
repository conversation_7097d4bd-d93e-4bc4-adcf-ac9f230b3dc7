'use client';

import { motion } from 'framer-motion';

export default function EventDetails() {
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { y: 50, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.8
      }
    }
  };

  const events = [
    {
      title: "Akad Nikah",
      date: "Sabtu, 15 Juli 2024",
      time: "09.00 - 11.00 WIB",
      location: "Masjid Al-Ikhlas",
      address: "Jl. Merdeka No. 123, Jakarta Pusat",
      icon: "🕌",
      color: "from-emerald-400 to-teal-500"
    },
    {
      title: "Rese<PERSON><PERSON>nikahan",
      date: "Sabtu, 15 Juli 2024",
      time: "18.00 - 21.00 WIB",
      location: "Gedung Serbaguna Melati",
      address: "Jl. Bunga Raya No. 456, Jakarta Pusat",
      icon: "🎉",
      color: "from-rose-400 to-pink-500"
    }
  ];

  return (
    <section className="py-20 px-4 bg-cream">
      <div className="max-w-6xl mx-auto">
        <motion.div
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.3 }}
          variants={containerVariants}
        >
          <motion.h2
            variants={itemVariants}
            className="text-4xl md:text-5xl font-bold text-center ornament-gold mb-8"
          >
            Detail Acara
          </motion.h2>

          <motion.div
            variants={itemVariants}
            className="text-center mb-16"
          >
            <div className="w-24 h-1 bg-gold-gradient mx-auto mb-4"></div>
            <div className="text-2xl text-gold">❋ ✦ ❋</div>
          </motion.div>

          <div className="grid md:grid-cols-2 gap-8">
            {events.map((event, index) => (
              <motion.div
                key={index}
                variants={itemVariants}
                whileHover={{ y: -10 }}
                className="bg-cream-light rounded-3xl shadow-gold p-8 relative overflow-hidden border-2 border-gold"
              >
                {/* Background Pattern */}
                <div className="absolute top-0 right-0 w-32 h-32 opacity-10">
                  <div className="w-full h-full bg-gold-gradient rounded-full transform translate-x-8 -translate-y-8"></div>
                </div>

                <div className="relative z-10">
                  <div className="text-center mb-6">
                    <motion.div
                      animate={{ 
                        rotate: [0, 10, -10, 0],
                        scale: [1, 1.1, 1]
                      }}
                      transition={{ 
                        duration: 3, 
                        repeat: Infinity,
                        ease: "easeInOut"
                      }}
                      className="text-6xl mb-4"
                    >
                      {event.icon}
                    </motion.div>
                    <h3 className="text-2xl font-bold ornament-gold mb-2">
                      {event.title}
                    </h3>
                  </div>

                  <div className="space-y-4">
                    <div className="flex items-center">
                      <div className="text-2xl mr-3">📅</div>
                      <div>
                        <p className="font-semibold text-dark-brown">{event.date}</p>
                      </div>
                    </div>

                    <div className="flex items-center">
                      <div className="text-2xl mr-3">⏰</div>
                      <div>
                        <p className="font-semibold text-dark-brown">{event.time}</p>
                      </div>
                    </div>

                    <div className="flex items-start">
                      <div className="text-2xl mr-3 mt-1">📍</div>
                      <div>
                        <p className="font-semibold text-dark-brown mb-1">{event.location}</p>
                        <p className="text-gold text-sm">{event.address}</p>
                      </div>
                    </div>
                  </div>

                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    className={`w-full mt-6 bg-gradient-to-r ${event.color} text-white py-3 rounded-full font-semibold shadow-lg hover:shadow-xl transition-all duration-300`}
                    onClick={() => {
                      const address = encodeURIComponent(event.address);
                      window.open(`https://maps.google.com/maps?q=${address}`, '_blank');
                    }}
                  >
                    Lihat Lokasi
                  </motion.button>
                </div>
              </motion.div>
            ))}
          </div>

          {/* Countdown Timer */}
          <motion.div 
            variants={itemVariants}
            className="mt-16 text-center"
          >
            <h3 className="text-2xl font-bold text-gray-800 mb-8">
              Menuju Hari Bahagia
            </h3>
            <div className="grid grid-cols-4 gap-4 max-w-md mx-auto">
              {['Hari', 'Jam', 'Menit', 'Detik'].map((unit, index) => (
                <motion.div
                  key={unit}
                  whileHover={{ scale: 1.05 }}
                  className="bg-white rounded-2xl shadow-lg p-4"
                >
                  <motion.div
                    animate={{ scale: [1, 1.1, 1] }}
                    transition={{ 
                      duration: 2, 
                      repeat: Infinity,
                      delay: index * 0.2
                    }}
                    className="text-2xl font-bold text-rose-500 mb-1"
                  >
                    {index === 0 ? '30' : index === 1 ? '12' : index === 2 ? '45' : '30'}
                  </motion.div>
                  <div className="text-sm text-gray-600">{unit}</div>
                </motion.div>
              ))}
            </div>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
}
