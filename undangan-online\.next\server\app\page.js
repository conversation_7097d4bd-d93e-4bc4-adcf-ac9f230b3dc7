(()=>{var e={};e.id=974,e.ids=[974],e.modules={440:(e,t,i)=>{"use strict";i.r(t),i.d(t,{default:()=>r});var n=i(1658);let r=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,n.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},512:(e,t,i)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var i in t)Object.defineProperty(e,i,{enumerable:!0,get:t[i]})}(t,{default:function(){return f},defaultHead:function(){return c}});let n=i(4985),r=i(740),s=i(687),a=r._(i(3210)),o=n._(i(7755)),l=i(4959),u=i(9513),d=i(4604);function c(e){void 0===e&&(e=!1);let t=[(0,s.jsx)("meta",{charSet:"utf-8"},"charset")];return e||t.push((0,s.jsx)("meta",{name:"viewport",content:"width=device-width"},"viewport")),t}function h(e,t){return"string"==typeof t||"number"==typeof t?e:t.type===a.default.Fragment?e.concat(a.default.Children.toArray(t.props.children).reduce((e,t)=>"string"==typeof t||"number"==typeof t?e:e.concat(t),[])):e.concat(t)}i(148);let p=["name","httpEquiv","charSet","itemProp"];function m(e,t){let{inAmpMode:i}=t;return e.reduce(h,[]).reverse().concat(c(i).reverse()).filter(function(){let e=new Set,t=new Set,i=new Set,n={};return r=>{let s=!0,a=!1;if(r.key&&"number"!=typeof r.key&&r.key.indexOf("$")>0){a=!0;let t=r.key.slice(r.key.indexOf("$")+1);e.has(t)?s=!1:e.add(t)}switch(r.type){case"title":case"base":t.has(r.type)?s=!1:t.add(r.type);break;case"meta":for(let e=0,t=p.length;e<t;e++){let t=p[e];if(r.props.hasOwnProperty(t))if("charSet"===t)i.has(t)?s=!1:i.add(t);else{let e=r.props[t],i=n[t]||new Set;("name"!==t||!a)&&i.has(e)?s=!1:(i.add(e),n[t]=i)}}}return s}}()).reverse().map((e,t)=>{let n=e.key||t;if(process.env.__NEXT_OPTIMIZE_FONTS&&!i&&"link"===e.type&&e.props.href&&["https://fonts.googleapis.com/css","https://use.typekit.net/"].some(t=>e.props.href.startsWith(t))){let t={...e.props||{}};return t["data-href"]=t.href,t.href=void 0,t["data-optimized-fonts"]=!0,a.default.cloneElement(e,t)}return a.default.cloneElement(e,{key:n})})}let f=function(e){let{children:t}=e,i=(0,a.useContext)(l.AmpStateContext),n=(0,a.useContext)(u.HeadManagerContext);return(0,s.jsx)(o.default,{reduceComponentsToState:m,headManager:n,inAmpMode:(0,d.isInAmpMode)(i),children:t})};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},554:(e,t)=>{"use strict";function i(e){return e.endsWith("/route")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isAppRouteRoute",{enumerable:!0,get:function(){return i}})},660:(e,t)=>{"use strict";function i(e){let t=5381;for(let i=0;i<e.length;i++)t=(t<<5)+t+e.charCodeAt(i)|0;return t>>>0}function n(e){return i(e).toString(36).slice(0,5)}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var i in t)Object.defineProperty(e,i,{enumerable:!0,get:t[i]})}(t,{djb2Hash:function(){return i},hexHash:function(){return n}})},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},923:(e,t,i)=>{Promise.resolve().then(i.t.bind(i,6444,23)),Promise.resolve().then(i.t.bind(i,6042,23)),Promise.resolve().then(i.t.bind(i,8170,23)),Promise.resolve().then(i.t.bind(i,9477,23)),Promise.resolve().then(i.t.bind(i,9345,23)),Promise.resolve().then(i.t.bind(i,2089,23)),Promise.resolve().then(i.t.bind(i,6577,23)),Promise.resolve().then(i.t.bind(i,1307,23))},1135:()=>{},1204:(e,t,i)=>{"use strict";i.r(t),i.d(t,{default:()=>s});var n=i(7413),r=i(9769);function s(){return(0,n.jsx)(r.default,{})}},1261:(e,t,i)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var i in t)Object.defineProperty(e,i,{enumerable:!0,get:t[i]})}(t,{default:function(){return l},getImageProps:function(){return o}});let n=i(4985),r=i(4953),s=i(6533),a=n._(i(1933));function o(e){let{props:t}=(0,r.getImgProps)(e,{defaultLoader:a.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1}});for(let[e,i]of Object.entries(t))void 0===i&&delete t[e];return{props:t}}let l=s.Image},1437:(e,t,i)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var i in t)Object.defineProperty(e,i,{enumerable:!0,get:t[i]})}(t,{INTERCEPTION_ROUTE_MARKERS:function(){return r},extractInterceptionRouteInformation:function(){return a},isInterceptionRouteAppPath:function(){return s}});let n=i(4722),r=["(..)(..)","(.)","(..)","(...)"];function s(e){return void 0!==e.split("/").find(e=>r.find(t=>e.startsWith(t)))}function a(e){let t,i,s;for(let n of e.split("/"))if(i=r.find(e=>n.startsWith(e))){[t,s]=e.split(i,2);break}if(!t||!i||!s)throw Object.defineProperty(Error("Invalid interception route: "+e+". Must be in the format /<intercepting route>/(..|...|..)(..)/<intercepted route>"),"__NEXT_ERROR_CODE",{value:"E269",enumerable:!1,configurable:!0});switch(t=(0,n.normalizeAppPath)(t),i){case"(.)":s="/"===t?"/"+s:t+"/"+s;break;case"(..)":if("/"===t)throw Object.defineProperty(Error("Invalid interception route: "+e+". Cannot use (..) marker at the root level, use (.) instead."),"__NEXT_ERROR_CODE",{value:"E207",enumerable:!1,configurable:!0});s=t.split("/").slice(0,-1).concat(s).join("/");break;case"(...)":s="/"+s;break;case"(..)(..)":let a=t.split("/");if(a.length<=2)throw Object.defineProperty(Error("Invalid interception route: "+e+". Cannot use (..)(..) marker at the root level or one level up."),"__NEXT_ERROR_CODE",{value:"E486",enumerable:!1,configurable:!0});s=a.slice(0,-2).concat(s).join("/");break;default:throw Object.defineProperty(Error("Invariant: unexpected marker"),"__NEXT_ERROR_CODE",{value:"E112",enumerable:!1,configurable:!0})}return{interceptingRoute:t,interceptedRoute:s}}},1440:()=>{},1480:(e,t)=>{"use strict";function i(e){let{widthInt:t,heightInt:i,blurWidth:n,blurHeight:r,blurDataURL:s,objectFit:a}=e,o=n?40*n:t,l=r?40*r:i,u=o&&l?"viewBox='0 0 "+o+" "+l+"'":"";return"%3Csvg xmlns='http://www.w3.org/2000/svg' "+u+"%3E%3Cfilter id='b' color-interpolation-filters='sRGB'%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3CfeColorMatrix values='1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 100 -1' result='s'/%3E%3CfeFlood x='0' y='0' width='100%25' height='100%25'/%3E%3CfeComposite operator='out' in='s'/%3E%3CfeComposite in2='SourceGraphic'/%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3C/filter%3E%3Cimage width='100%25' height='100%25' x='0' y='0' preserveAspectRatio='"+(u?"none":"contain"===a?"xMidYMid":"cover"===a?"xMidYMid slice":"none")+"' style='filter: url(%23b);' href='"+s+"'/%3E%3C/svg%3E"}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImageBlurSvg",{enumerable:!0,get:function(){return i}})},1658:(e,t,i)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var i in t)Object.defineProperty(e,i,{enumerable:!0,get:t[i]})}(t,{fillMetadataSegment:function(){return h},normalizeMetadataPageToRoute:function(){return m},normalizeMetadataRoute:function(){return p}});let n=i(8304),r=function(e){return e&&e.__esModule?e:{default:e}}(i(8671)),s=i(6341),a=i(4396),o=i(660),l=i(4722),u=i(2958),d=i(5499);function c(e){let t=r.default.dirname(e);if(e.endsWith("/sitemap"))return"";let i="";return t.split("/").some(e=>(0,d.isGroupSegment)(e)||(0,d.isParallelRouteSegment)(e))&&(i=(0,o.djb2Hash)(t).toString(36).slice(0,6)),i}function h(e,t,i){let n=(0,l.normalizeAppPath)(e),o=(0,a.getNamedRouteRegex)(n,{prefixRouteKeys:!1}),d=(0,s.interpolateDynamicPath)(n,t,o),{name:h,ext:p}=r.default.parse(i),m=c(r.default.posix.join(e,h)),f=m?`-${m}`:"";return(0,u.normalizePathSep)(r.default.join(d,`${h}${f}${p}`))}function p(e){if(!(0,n.isMetadataPage)(e))return e;let t=e,i="";if("/robots"===e?t+=".txt":"/manifest"===e?t+=".webmanifest":i=c(e),!t.endsWith("/route")){let{dir:e,name:n,ext:s}=r.default.parse(t);t=r.default.posix.join(e,`${n}${i?`-${i}`:""}${s}`,"route")}return t}function m(e,t){let i=e.endsWith("/route"),n=i?e.slice(0,-6):e,r=n.endsWith("/sitemap")?".xml":"";return(t?`${n}/[__metadata_id__]`:`${n}${r}`)+(i?"/route":"")}},1904:(e,t,i)=>{"use strict";i.r(t),i.d(t,{GlobalError:()=>a.a,__next_app__:()=>c,pages:()=>d,routeModule:()=>h,tree:()=>u});var n=i(5239),r=i(8088),s=i(8170),a=i.n(s),o=i(893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);i.d(t,l);let u={children:["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(i.bind(i,1204)),"D:\\Program\\laragon\\www\\undangan\\undangan-online\\src\\app\\page.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(i.bind(i,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(i.bind(i,4431)),"D:\\Program\\laragon\\www\\undangan\\undangan-online\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(i.t.bind(i,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(i.t.bind(i,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(i.t.bind(i,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(i.bind(i,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["D:\\Program\\laragon\\www\\undangan\\undangan-online\\src\\app\\page.tsx"],c={require:i,loadChunk:()=>Promise.resolve()},h=new n.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:u}})},1933:(e,t)=>{"use strict";function i(e){var t;let{config:i,src:n,width:r,quality:s}=e,a=s||(null==(t=i.qualities)?void 0:t.reduce((e,t)=>Math.abs(t-75)<Math.abs(e-75)?t:e))||75;return i.path+"?url="+encodeURIComponent(n)+"&w="+r+"&q="+a+(n.startsWith("/_next/static/media/"),"")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return n}}),i.__next_img_default=!0;let n=i},2437:(e,t,i)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getPathMatch",{enumerable:!0,get:function(){return r}});let n=i(5362);function r(e,t){let i=[],r=(0,n.pathToRegexp)(e,i,{delimiter:"/",sensitive:"boolean"==typeof(null==t?void 0:t.sensitive)&&t.sensitive,strict:null==t?void 0:t.strict}),s=(0,n.regexpToFunction)((null==t?void 0:t.regexModifier)?new RegExp(t.regexModifier(r.source),r.flags):r,i);return(e,n)=>{if("string"!=typeof e)return!1;let r=s(e);if(!r)return!1;if(null==t?void 0:t.removeUnnamedParams)for(let e of i)"number"==typeof e.name&&delete r.params[e.name];return{...n,...r.params}}}},2756:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var i in t)Object.defineProperty(e,i,{enumerable:!0,get:t[i]})}(t,{VALID_LOADERS:function(){return i},imageConfigDefault:function(){return n}});let i=["default","imgix","cloudinary","akamai","custom"],n={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"attachment",localPatterns:void 0,remotePatterns:[],qualities:void 0,unoptimized:!1}},2785:(e,t)=>{"use strict";function i(e){let t={};for(let[i,n]of e.entries()){let e=t[i];void 0===e?t[i]=n:Array.isArray(e)?e.push(n):t[i]=[e,n]}return t}function n(e){return"string"==typeof e?e:("number"!=typeof e||isNaN(e))&&"boolean"!=typeof e?"":String(e)}function r(e){let t=new URLSearchParams;for(let[i,r]of Object.entries(e))if(Array.isArray(r))for(let e of r)t.append(i,n(e));else t.set(i,n(r));return t}function s(e){for(var t=arguments.length,i=Array(t>1?t-1:0),n=1;n<t;n++)i[n-1]=arguments[n];for(let t of i){for(let i of t.keys())e.delete(i);for(let[i,n]of t.entries())e.append(i,n)}return e}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var i in t)Object.defineProperty(e,i,{enumerable:!0,get:t[i]})}(t,{assign:function(){return s},searchParamsToUrlQuery:function(){return i},urlQueryToSearchParams:function(){return r}})},2958:(e,t)=>{"use strict";function i(e){return e.replace(/\\/g,"/")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"normalizePathSep",{enumerable:!0,get:function(){return i}})},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3038:(e,t,i)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"useMergedRef",{enumerable:!0,get:function(){return r}});let n=i(3210);function r(e,t){let i=(0,n.useRef)(null),r=(0,n.useRef)(null);return(0,n.useCallback)(n=>{if(null===n){let e=i.current;e&&(i.current=null,e());let t=r.current;t&&(r.current=null,t())}else e&&(i.current=s(e,n)),t&&(r.current=s(t,n))},[e,t])}function s(e,t){if("function"!=typeof e)return e.current=t,()=>{e.current=null};{let i=e(t);return"function"==typeof i?i:()=>e(null)}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},3293:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"escapeStringRegexp",{enumerable:!0,get:function(){return r}});let i=/[|\\{}()[\]^$+*?.-]/,n=/[|\\{}()[\]^$+*?.-]/g;function r(e){return i.test(e)?e.replace(n,"\\$&"):e}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3371:(e,t,i)=>{Promise.resolve().then(i.bind(i,8780))},3736:(e,t,i)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"parseRelativeUrl",{enumerable:!0,get:function(){return r}}),i(4827);let n=i(2785);function r(e,t,i){void 0===i&&(i=!0);let r=new URL("http://n"),s=t?new URL(t,r):e.startsWith(".")?new URL("http://n"):r,{pathname:a,searchParams:o,search:l,hash:u,href:d,origin:c}=new URL(e,s);if(c!==r.origin)throw Object.defineProperty(Error("invariant: invalid relative URL, router received "+e),"__NEXT_ERROR_CODE",{value:"E159",enumerable:!1,configurable:!0});return{pathname:a,query:i?(0,n.searchParamsToUrlQuery)(o):void 0,search:l,hash:u,href:d.slice(c.length)}}},3873:e=>{"use strict";e.exports=require("path")},4396:(e,t,i)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var i in t)Object.defineProperty(e,i,{enumerable:!0,get:t[i]})}(t,{getNamedMiddlewareRegex:function(){return f},getNamedRouteRegex:function(){return m},getRouteRegex:function(){return c},parseParameter:function(){return l}});let n=i(6143),r=i(1437),s=i(3293),a=i(2887),o=/^([^[]*)\[((?:\[[^\]]*\])|[^\]]+)\](.*)$/;function l(e){let t=e.match(o);return t?u(t[2]):u(e)}function u(e){let t=e.startsWith("[")&&e.endsWith("]");t&&(e=e.slice(1,-1));let i=e.startsWith("...");return i&&(e=e.slice(3)),{key:e,repeat:i,optional:t}}function d(e,t,i){let n={},l=1,d=[];for(let c of(0,a.removeTrailingSlash)(e).slice(1).split("/")){let e=r.INTERCEPTION_ROUTE_MARKERS.find(e=>c.startsWith(e)),a=c.match(o);if(e&&a&&a[2]){let{key:t,optional:i,repeat:r}=u(a[2]);n[t]={pos:l++,repeat:r,optional:i},d.push("/"+(0,s.escapeStringRegexp)(e)+"([^/]+?)")}else if(a&&a[2]){let{key:e,repeat:t,optional:r}=u(a[2]);n[e]={pos:l++,repeat:t,optional:r},i&&a[1]&&d.push("/"+(0,s.escapeStringRegexp)(a[1]));let o=t?r?"(?:/(.+?))?":"/(.+?)":"/([^/]+?)";i&&a[1]&&(o=o.substring(1)),d.push(o)}else d.push("/"+(0,s.escapeStringRegexp)(c));t&&a&&a[3]&&d.push((0,s.escapeStringRegexp)(a[3]))}return{parameterizedRoute:d.join(""),groups:n}}function c(e,t){let{includeSuffix:i=!1,includePrefix:n=!1,excludeOptionalTrailingSlash:r=!1}=void 0===t?{}:t,{parameterizedRoute:s,groups:a}=d(e,i,n),o=s;return r||(o+="(?:/)?"),{re:RegExp("^"+o+"$"),groups:a}}function h(e){let t,{interceptionMarker:i,getSafeRouteKey:n,segment:r,routeKeys:a,keyPrefix:o,backreferenceDuplicateKeys:l}=e,{key:d,optional:c,repeat:h}=u(r),p=d.replace(/\W/g,"");o&&(p=""+o+p);let m=!1;(0===p.length||p.length>30)&&(m=!0),isNaN(parseInt(p.slice(0,1)))||(m=!0),m&&(p=n());let f=p in a;o?a[p]=""+o+d:a[p]=d;let g=i?(0,s.escapeStringRegexp)(i):"";return t=f&&l?"\\k<"+p+">":h?"(?<"+p+">.+?)":"(?<"+p+">[^/]+?)",c?"(?:/"+g+t+")?":"/"+g+t}function p(e,t,i,l,u){let d,c=(d=0,()=>{let e="",t=++d;for(;t>0;)e+=String.fromCharCode(97+(t-1)%26),t=Math.floor((t-1)/26);return e}),p={},m=[];for(let d of(0,a.removeTrailingSlash)(e).slice(1).split("/")){let e=r.INTERCEPTION_ROUTE_MARKERS.some(e=>d.startsWith(e)),a=d.match(o);if(e&&a&&a[2])m.push(h({getSafeRouteKey:c,interceptionMarker:a[1],segment:a[2],routeKeys:p,keyPrefix:t?n.NEXT_INTERCEPTION_MARKER_PREFIX:void 0,backreferenceDuplicateKeys:u}));else if(a&&a[2]){l&&a[1]&&m.push("/"+(0,s.escapeStringRegexp)(a[1]));let e=h({getSafeRouteKey:c,segment:a[2],routeKeys:p,keyPrefix:t?n.NEXT_QUERY_PARAM_PREFIX:void 0,backreferenceDuplicateKeys:u});l&&a[1]&&(e=e.substring(1)),m.push(e)}else m.push("/"+(0,s.escapeStringRegexp)(d));i&&a&&a[3]&&m.push((0,s.escapeStringRegexp)(a[3]))}return{namedParameterizedRoute:m.join(""),routeKeys:p}}function m(e,t){var i,n,r;let s=p(e,t.prefixRouteKeys,null!=(i=t.includeSuffix)&&i,null!=(n=t.includePrefix)&&n,null!=(r=t.backreferenceDuplicateKeys)&&r),a=s.namedParameterizedRoute;return t.excludeOptionalTrailingSlash||(a+="(?:/)?"),{...c(e,t),namedRegex:"^"+a+"$",routeKeys:s.routeKeys}}function f(e,t){let{parameterizedRoute:i}=d(e,!1,!1),{catchAll:n=!0}=t;if("/"===i)return{namedRegex:"^/"+(n?".*":"")+"$"};let{namedParameterizedRoute:r}=p(e,!1,!1,!1,!1);return{namedRegex:"^"+r+(n?"(?:(/.*)?)":"")+"$"}}},4431:(e,t,i)=>{"use strict";i.r(t),i.d(t,{default:()=>u,metadata:()=>l});var n=i(7413),r=i(2376),s=i.n(r),a=i(8726),o=i.n(a);i(1135);let l={title:"Create Next App",description:"Generated by create next app"};function u({children:e}){return(0,n.jsx)("html",{lang:"en",children:(0,n.jsx)("body",{className:`${s().variable} ${o().variable} antialiased`,children:e})})}},4604:(e,t)=>{"use strict";function i(e){let{ampFirst:t=!1,hybrid:i=!1,hasQuery:n=!1}=void 0===e?{}:e;return t||i&&n}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isInAmpMode",{enumerable:!0,get:function(){return i}})},4722:(e,t,i)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var i in t)Object.defineProperty(e,i,{enumerable:!0,get:t[i]})}(t,{normalizeAppPath:function(){return s},normalizeRscURL:function(){return a}});let n=i(5531),r=i(5499);function s(e){return(0,n.ensureLeadingSlash)(e.split("/").reduce((e,t,i,n)=>!t||(0,r.isGroupSegment)(t)||"@"===t[0]||("page"===t||"route"===t)&&i===n.length-1?e:e+"/"+t,""))}function a(e){return e.replace(/\.rsc($|\?)/,"$1")}},4827:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var i in t)Object.defineProperty(e,i,{enumerable:!0,get:t[i]})}(t,{DecodeError:function(){return m},MiddlewareNotFoundError:function(){return v},MissingStaticPage:function(){return y},NormalizeError:function(){return f},PageNotFoundError:function(){return g},SP:function(){return h},ST:function(){return p},WEB_VITALS:function(){return i},execOnce:function(){return n},getDisplayName:function(){return l},getLocationOrigin:function(){return a},getURL:function(){return o},isAbsoluteUrl:function(){return s},isResSent:function(){return u},loadGetInitialProps:function(){return c},normalizeRepeatedSlashes:function(){return d},stringifyError:function(){return x}});let i=["CLS","FCP","FID","INP","LCP","TTFB"];function n(e){let t,i=!1;return function(){for(var n=arguments.length,r=Array(n),s=0;s<n;s++)r[s]=arguments[s];return i||(i=!0,t=e(...r)),t}}let r=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,s=e=>r.test(e);function a(){let{protocol:e,hostname:t,port:i}=window.location;return e+"//"+t+(i?":"+i:"")}function o(){let{href:e}=window.location,t=a();return e.substring(t.length)}function l(e){return"string"==typeof e?e:e.displayName||e.name||"Unknown"}function u(e){return e.finished||e.headersSent}function d(e){let t=e.split("?");return t[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(t[1]?"?"+t.slice(1).join("?"):"")}async function c(e,t){let i=t.res||t.ctx&&t.ctx.res;if(!e.getInitialProps)return t.ctx&&t.Component?{pageProps:await c(t.Component,t.ctx)}:{};let n=await e.getInitialProps(t);if(i&&u(i))return n;if(!n)throw Object.defineProperty(Error('"'+l(e)+'.getInitialProps()" should resolve to an object. But found "'+n+'" instead.'),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return n}let h="undefined"!=typeof performance,p=h&&["mark","measure","getEntriesByName"].every(e=>"function"==typeof performance[e]);class m extends Error{}class f extends Error{}class g extends Error{constructor(e){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+e}}class y extends Error{constructor(e,t){super(),this.message="Failed to load static file for page: "+e+" "+t}}class v extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function x(e){return JSON.stringify({message:e.message,stack:e.stack})}},4953:(e,t,i)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImgProps",{enumerable:!0,get:function(){return l}}),i(148);let n=i(1480),r=i(2756),s=["-moz-initial","fill","none","scale-down",void 0];function a(e){return void 0!==e.default}function o(e){return void 0===e?e:"number"==typeof e?Number.isFinite(e)?e:NaN:"string"==typeof e&&/^[0-9]+$/.test(e)?parseInt(e,10):NaN}function l(e,t){var i,l;let u,d,c,{src:h,sizes:p,unoptimized:m=!1,priority:f=!1,loading:g,className:y,quality:v,width:x,height:b,fill:w=!1,style:j,overrideSrc:P,onLoad:S,onLoadingComplete:E,placeholder:T="empty",blurDataURL:A,fetchPriority:R,decoding:k="async",layout:C,objectFit:_,objectPosition:M,lazyBoundary:N,lazyRoot:D,...O}=e,{imgConf:V,showAltText:I,blurComplete:F,defaultLoader:L}=t,$=V||r.imageConfigDefault;if("allSizes"in $)u=$;else{let e=[...$.deviceSizes,...$.imageSizes].sort((e,t)=>e-t),t=$.deviceSizes.sort((e,t)=>e-t),n=null==(i=$.qualities)?void 0:i.sort((e,t)=>e-t);u={...$,allSizes:e,deviceSizes:t,qualities:n}}if(void 0===L)throw Object.defineProperty(Error("images.loaderFile detected but the file is missing default export.\nRead more: https://nextjs.org/docs/messages/invalid-images-config"),"__NEXT_ERROR_CODE",{value:"E163",enumerable:!1,configurable:!0});let B=O.loader||L;delete O.loader,delete O.srcSet;let U="__next_img_default"in B;if(U){if("custom"===u.loader)throw Object.defineProperty(Error('Image with src "'+h+'" is missing "loader" prop.\nRead more: https://nextjs.org/docs/messages/next-image-missing-loader'),"__NEXT_ERROR_CODE",{value:"E252",enumerable:!1,configurable:!0})}else{let e=B;B=t=>{let{config:i,...n}=t;return e(n)}}if(C){"fill"===C&&(w=!0);let e={intrinsic:{maxWidth:"100%",height:"auto"},responsive:{width:"100%",height:"auto"}}[C];e&&(j={...j,...e});let t={responsive:"100vw",fill:"100vw"}[C];t&&!p&&(p=t)}let z="",W=o(x),H=o(b);if((l=h)&&"object"==typeof l&&(a(l)||void 0!==l.src)){let e=a(h)?h.default:h;if(!e.src)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include src. Received "+JSON.stringify(e)),"__NEXT_ERROR_CODE",{value:"E460",enumerable:!1,configurable:!0});if(!e.height||!e.width)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include height and width. Received "+JSON.stringify(e)),"__NEXT_ERROR_CODE",{value:"E48",enumerable:!1,configurable:!0});if(d=e.blurWidth,c=e.blurHeight,A=A||e.blurDataURL,z=e.src,!w)if(W||H){if(W&&!H){let t=W/e.width;H=Math.round(e.height*t)}else if(!W&&H){let t=H/e.height;W=Math.round(e.width*t)}}else W=e.width,H=e.height}let q=!f&&("lazy"===g||void 0===g);(!(h="string"==typeof h?h:z)||h.startsWith("data:")||h.startsWith("blob:"))&&(m=!0,q=!1),u.unoptimized&&(m=!0),U&&!u.dangerouslyAllowSVG&&h.split("?",1)[0].endsWith(".svg")&&(m=!0);let X=o(v),K=Object.assign(w?{position:"absolute",height:"100%",width:"100%",left:0,top:0,right:0,bottom:0,objectFit:_,objectPosition:M}:{},I?{}:{color:"transparent"},j),G=F||"empty"===T?null:"blur"===T?'url("data:image/svg+xml;charset=utf-8,'+(0,n.getImageBlurSvg)({widthInt:W,heightInt:H,blurWidth:d,blurHeight:c,blurDataURL:A||"",objectFit:K.objectFit})+'")':'url("'+T+'")',Y=s.includes(K.objectFit)?"fill"===K.objectFit?"100% 100%":"cover":K.objectFit,Z=G?{backgroundSize:Y,backgroundPosition:K.objectPosition||"50% 50%",backgroundRepeat:"no-repeat",backgroundImage:G}:{},J=function(e){let{config:t,src:i,unoptimized:n,width:r,quality:s,sizes:a,loader:o}=e;if(n)return{src:i,srcSet:void 0,sizes:void 0};let{widths:l,kind:u}=function(e,t,i){let{deviceSizes:n,allSizes:r}=e;if(i){let e=/(^|\s)(1?\d?\d)vw/g,t=[];for(let n;n=e.exec(i);)t.push(parseInt(n[2]));if(t.length){let e=.01*Math.min(...t);return{widths:r.filter(t=>t>=n[0]*e),kind:"w"}}return{widths:r,kind:"w"}}return"number"!=typeof t?{widths:n,kind:"w"}:{widths:[...new Set([t,2*t].map(e=>r.find(t=>t>=e)||r[r.length-1]))],kind:"x"}}(t,r,a),d=l.length-1;return{sizes:a||"w"!==u?a:"100vw",srcSet:l.map((e,n)=>o({config:t,src:i,quality:s,width:e})+" "+("w"===u?e:n+1)+u).join(", "),src:o({config:t,src:i,quality:s,width:l[d]})}}({config:u,src:h,unoptimized:m,width:W,quality:X,sizes:p,loader:B});return{props:{...O,loading:q?"lazy":g,fetchPriority:R,width:W,height:H,decoding:k,className:y,style:{...K,...Z},sizes:J.sizes,srcSet:J.srcSet,src:P||J.src},meta:{unoptimized:m,priority:f,placeholder:T,fill:w}}}},4959:(e,t,i)=>{"use strict";e.exports=i(4041).vendored.contexts.AmpContext},5362:e=>{(()=>{"use strict";"undefined"!=typeof __nccwpck_require__&&(__nccwpck_require__.ab=__dirname+"/");var t={};(()=>{function e(e,t){void 0===t&&(t={});for(var i=function(e){for(var t=[],i=0;i<e.length;){var n=e[i];if("*"===n||"+"===n||"?"===n){t.push({type:"MODIFIER",index:i,value:e[i++]});continue}if("\\"===n){t.push({type:"ESCAPED_CHAR",index:i++,value:e[i++]});continue}if("{"===n){t.push({type:"OPEN",index:i,value:e[i++]});continue}if("}"===n){t.push({type:"CLOSE",index:i,value:e[i++]});continue}if(":"===n){for(var r="",s=i+1;s<e.length;){var a=e.charCodeAt(s);if(a>=48&&a<=57||a>=65&&a<=90||a>=97&&a<=122||95===a){r+=e[s++];continue}break}if(!r)throw TypeError("Missing parameter name at "+i);t.push({type:"NAME",index:i,value:r}),i=s;continue}if("("===n){var o=1,l="",s=i+1;if("?"===e[s])throw TypeError('Pattern cannot start with "?" at '+s);for(;s<e.length;){if("\\"===e[s]){l+=e[s++]+e[s++];continue}if(")"===e[s]){if(0==--o){s++;break}}else if("("===e[s]&&(o++,"?"!==e[s+1]))throw TypeError("Capturing groups are not allowed at "+s);l+=e[s++]}if(o)throw TypeError("Unbalanced pattern at "+i);if(!l)throw TypeError("Missing pattern at "+i);t.push({type:"PATTERN",index:i,value:l}),i=s;continue}t.push({type:"CHAR",index:i,value:e[i++]})}return t.push({type:"END",index:i,value:""}),t}(e),n=t.prefixes,s=void 0===n?"./":n,a="[^"+r(t.delimiter||"/#?")+"]+?",o=[],l=0,u=0,d="",c=function(e){if(u<i.length&&i[u].type===e)return i[u++].value},h=function(e){var t=c(e);if(void 0!==t)return t;var n=i[u];throw TypeError("Unexpected "+n.type+" at "+n.index+", expected "+e)},p=function(){for(var e,t="";e=c("CHAR")||c("ESCAPED_CHAR");)t+=e;return t};u<i.length;){var m=c("CHAR"),f=c("NAME"),g=c("PATTERN");if(f||g){var y=m||"";-1===s.indexOf(y)&&(d+=y,y=""),d&&(o.push(d),d=""),o.push({name:f||l++,prefix:y,suffix:"",pattern:g||a,modifier:c("MODIFIER")||""});continue}var v=m||c("ESCAPED_CHAR");if(v){d+=v;continue}if(d&&(o.push(d),d=""),c("OPEN")){var y=p(),x=c("NAME")||"",b=c("PATTERN")||"",w=p();h("CLOSE"),o.push({name:x||(b?l++:""),pattern:x&&!b?a:b,prefix:y,suffix:w,modifier:c("MODIFIER")||""});continue}h("END")}return o}function i(e,t){void 0===t&&(t={});var i=s(t),n=t.encode,r=void 0===n?function(e){return e}:n,a=t.validate,o=void 0===a||a,l=e.map(function(e){if("object"==typeof e)return RegExp("^(?:"+e.pattern+")$",i)});return function(t){for(var i="",n=0;n<e.length;n++){var s=e[n];if("string"==typeof s){i+=s;continue}var a=t?t[s.name]:void 0,u="?"===s.modifier||"*"===s.modifier,d="*"===s.modifier||"+"===s.modifier;if(Array.isArray(a)){if(!d)throw TypeError('Expected "'+s.name+'" to not repeat, but got an array');if(0===a.length){if(u)continue;throw TypeError('Expected "'+s.name+'" to not be empty')}for(var c=0;c<a.length;c++){var h=r(a[c],s);if(o&&!l[n].test(h))throw TypeError('Expected all "'+s.name+'" to match "'+s.pattern+'", but got "'+h+'"');i+=s.prefix+h+s.suffix}continue}if("string"==typeof a||"number"==typeof a){var h=r(String(a),s);if(o&&!l[n].test(h))throw TypeError('Expected "'+s.name+'" to match "'+s.pattern+'", but got "'+h+'"');i+=s.prefix+h+s.suffix;continue}if(!u){var p=d?"an array":"a string";throw TypeError('Expected "'+s.name+'" to be '+p)}}return i}}function n(e,t,i){void 0===i&&(i={});var n=i.decode,r=void 0===n?function(e){return e}:n;return function(i){var n=e.exec(i);if(!n)return!1;for(var s=n[0],a=n.index,o=Object.create(null),l=1;l<n.length;l++)!function(e){if(void 0!==n[e]){var i=t[e-1];"*"===i.modifier||"+"===i.modifier?o[i.name]=n[e].split(i.prefix+i.suffix).map(function(e){return r(e,i)}):o[i.name]=r(n[e],i)}}(l);return{path:s,index:a,params:o}}}function r(e){return e.replace(/([.+*?=^!:${}()[\]|/\\])/g,"\\$1")}function s(e){return e&&e.sensitive?"":"i"}function a(e,t,i){void 0===i&&(i={});for(var n=i.strict,a=void 0!==n&&n,o=i.start,l=i.end,u=i.encode,d=void 0===u?function(e){return e}:u,c="["+r(i.endsWith||"")+"]|$",h="["+r(i.delimiter||"/#?")+"]",p=void 0===o||o?"^":"",m=0;m<e.length;m++){var f=e[m];if("string"==typeof f)p+=r(d(f));else{var g=r(d(f.prefix)),y=r(d(f.suffix));if(f.pattern)if(t&&t.push(f),g||y)if("+"===f.modifier||"*"===f.modifier){var v="*"===f.modifier?"?":"";p+="(?:"+g+"((?:"+f.pattern+")(?:"+y+g+"(?:"+f.pattern+"))*)"+y+")"+v}else p+="(?:"+g+"("+f.pattern+")"+y+")"+f.modifier;else p+="("+f.pattern+")"+f.modifier;else p+="(?:"+g+y+")"+f.modifier}}if(void 0===l||l)a||(p+=h+"?"),p+=i.endsWith?"(?="+c+")":"$";else{var x=e[e.length-1],b="string"==typeof x?h.indexOf(x[x.length-1])>-1:void 0===x;a||(p+="(?:"+h+"(?="+c+"))?"),b||(p+="(?="+h+"|"+c+")")}return new RegExp(p,s(i))}function o(t,i,n){if(t instanceof RegExp){if(!i)return t;var r=t.source.match(/\((?!\?)/g);if(r)for(var l=0;l<r.length;l++)i.push({name:l,prefix:"",suffix:"",modifier:"",pattern:""});return t}return Array.isArray(t)?RegExp("(?:"+t.map(function(e){return o(e,i,n).source}).join("|")+")",s(n)):a(e(t,n),i,n)}Object.defineProperty(t,"__esModule",{value:!0}),t.parse=e,t.compile=function(t,n){return i(e(t,n),n)},t.tokensToFunction=i,t.match=function(e,t){var i=[];return n(o(e,i,t),i,t)},t.regexpToFunction=n,t.tokensToRegexp=a,t.pathToRegexp=o})(),e.exports=t})()},5526:(e,t,i)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var i in t)Object.defineProperty(e,i,{enumerable:!0,get:t[i]})}(t,{compileNonPath:function(){return d},matchHas:function(){return u},parseDestination:function(){return c},prepareDestination:function(){return h}});let n=i(5362),r=i(3293),s=i(6759),a=i(1437),o=i(8212);function l(e){return e.replace(/__ESC_COLON_/gi,":")}function u(e,t,i,n){void 0===i&&(i=[]),void 0===n&&(n=[]);let r={},s=i=>{let n,s=i.key;switch(i.type){case"header":s=s.toLowerCase(),n=e.headers[s];break;case"cookie":n="cookies"in e?e.cookies[i.key]:(0,o.getCookieParser)(e.headers)()[i.key];break;case"query":n=t[s];break;case"host":{let{host:t}=(null==e?void 0:e.headers)||{};n=null==t?void 0:t.split(":",1)[0].toLowerCase()}}if(!i.value&&n)return r[function(e){let t="";for(let i=0;i<e.length;i++){let n=e.charCodeAt(i);(n>64&&n<91||n>96&&n<123)&&(t+=e[i])}return t}(s)]=n,!0;if(n){let e=RegExp("^"+i.value+"$"),t=Array.isArray(n)?n.slice(-1)[0].match(e):n.match(e);if(t)return Array.isArray(t)&&(t.groups?Object.keys(t.groups).forEach(e=>{r[e]=t.groups[e]}):"host"===i.type&&t[0]&&(r.host=t[0])),!0}return!1};return!(!i.every(e=>s(e))||n.some(e=>s(e)))&&r}function d(e,t){if(!e.includes(":"))return e;for(let i of Object.keys(t))e.includes(":"+i)&&(e=e.replace(RegExp(":"+i+"\\*","g"),":"+i+"--ESCAPED_PARAM_ASTERISKS").replace(RegExp(":"+i+"\\?","g"),":"+i+"--ESCAPED_PARAM_QUESTION").replace(RegExp(":"+i+"\\+","g"),":"+i+"--ESCAPED_PARAM_PLUS").replace(RegExp(":"+i+"(?!\\w)","g"),"--ESCAPED_PARAM_COLON"+i));return e=e.replace(/(:|\*|\?|\+|\(|\)|\{|\})/g,"\\$1").replace(/--ESCAPED_PARAM_PLUS/g,"+").replace(/--ESCAPED_PARAM_COLON/g,":").replace(/--ESCAPED_PARAM_QUESTION/g,"?").replace(/--ESCAPED_PARAM_ASTERISKS/g,"*"),(0,n.compile)("/"+e,{validate:!1})(t).slice(1)}function c(e){let t=e.destination;for(let i of Object.keys({...e.params,...e.query}))i&&(t=t.replace(RegExp(":"+(0,r.escapeStringRegexp)(i),"g"),"__ESC_COLON_"+i));let i=(0,s.parseUrl)(t),n=i.pathname;n&&(n=l(n));let a=i.href;a&&(a=l(a));let o=i.hostname;o&&(o=l(o));let u=i.hash;return u&&(u=l(u)),{...i,pathname:n,hostname:o,href:a,hash:u}}function h(e){let t,i,r=Object.assign({},e.query),s=c(e),{hostname:o,query:u}=s,h=s.pathname;s.hash&&(h=""+h+s.hash);let p=[],m=[];for(let e of((0,n.pathToRegexp)(h,m),m))p.push(e.name);if(o){let e=[];for(let t of((0,n.pathToRegexp)(o,e),e))p.push(t.name)}let f=(0,n.compile)(h,{validate:!1});for(let[i,r]of(o&&(t=(0,n.compile)(o,{validate:!1})),Object.entries(u)))Array.isArray(r)?u[i]=r.map(t=>d(l(t),e.params)):"string"==typeof r&&(u[i]=d(l(r),e.params));let g=Object.keys(e.params).filter(e=>"nextInternalLocale"!==e);if(e.appendParamsToQuery&&!g.some(e=>p.includes(e)))for(let t of g)t in u||(u[t]=e.params[t]);if((0,a.isInterceptionRouteAppPath)(h))for(let t of h.split("/")){let i=a.INTERCEPTION_ROUTE_MARKERS.find(e=>t.startsWith(e));if(i){"(..)(..)"===i?(e.params["0"]="(..)",e.params["1"]="(..)"):e.params["0"]=i;break}}try{let[n,r]=(i=f(e.params)).split("#",2);t&&(s.hostname=t(e.params)),s.pathname=n,s.hash=(r?"#":"")+(r||""),delete s.search}catch(e){if(e.message.match(/Expected .*? to not repeat, but got an array/))throw Object.defineProperty(Error("To use a multi-match in the destination you must add `*` at the end of the param name to signify it should repeat. https://nextjs.org/docs/messages/invalid-multi-match"),"__NEXT_ERROR_CODE",{value:"E329",enumerable:!1,configurable:!0});throw e}return s.query={...r,...s.query},{newUrl:i,destQuery:u,parsedDestination:s}}},5531:(e,t)=>{"use strict";function i(e){return e.startsWith("/")?e:"/"+e}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"ensureLeadingSlash",{enumerable:!0,get:function(){return i}})},5913:(e,t,i)=>{"use strict";i(6397);var n=i(3210),r=function(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}(n),s="undefined"!=typeof process&&process.env&&!0,a=function(e){return"[object String]"===Object.prototype.toString.call(e)},o=function(){function e(e){var t=void 0===e?{}:e,i=t.name,n=void 0===i?"stylesheet":i,r=t.optimizeForSpeed,o=void 0===r?s:r;l(a(n),"`name` must be a string"),this._name=n,this._deletedRulePlaceholder="#"+n+"-deleted-rule____{}",l("boolean"==typeof o,"`optimizeForSpeed` must be a boolean"),this._optimizeForSpeed=o,this._serverSheet=void 0,this._tags=[],this._injected=!1,this._rulesCount=0,this._nonce=null}var t,i=e.prototype;return i.setOptimizeForSpeed=function(e){l("boolean"==typeof e,"`setOptimizeForSpeed` accepts a boolean"),l(0===this._rulesCount,"optimizeForSpeed cannot be when rules have already been inserted"),this.flush(),this._optimizeForSpeed=e,this.inject()},i.isOptimizeForSpeed=function(){return this._optimizeForSpeed},i.inject=function(){var e=this;l(!this._injected,"sheet already injected"),this._injected=!0,this._serverSheet={cssRules:[],insertRule:function(t,i){return"number"==typeof i?e._serverSheet.cssRules[i]={cssText:t}:e._serverSheet.cssRules.push({cssText:t}),i},deleteRule:function(t){e._serverSheet.cssRules[t]=null}}},i.getSheetForTag=function(e){if(e.sheet)return e.sheet;for(var t=0;t<document.styleSheets.length;t++)if(document.styleSheets[t].ownerNode===e)return document.styleSheets[t]},i.getSheet=function(){return this.getSheetForTag(this._tags[this._tags.length-1])},i.insertRule=function(e,t){return l(a(e),"`insertRule` accepts only strings"),"number"!=typeof t&&(t=this._serverSheet.cssRules.length),this._serverSheet.insertRule(e,t),this._rulesCount++},i.replaceRule=function(e,t){this._optimizeForSpeed;var i=this._serverSheet;if(t.trim()||(t=this._deletedRulePlaceholder),!i.cssRules[e])return e;i.deleteRule(e);try{i.insertRule(t,e)}catch(n){s||console.warn("StyleSheet: illegal rule: \n\n"+t+"\n\nSee https://stackoverflow.com/q/20007992 for more info"),i.insertRule(this._deletedRulePlaceholder,e)}return e},i.deleteRule=function(e){this._serverSheet.deleteRule(e)},i.flush=function(){this._injected=!1,this._rulesCount=0,this._serverSheet.cssRules=[]},i.cssRules=function(){return this._serverSheet.cssRules},i.makeStyleTag=function(e,t,i){t&&l(a(t),"makeStyleTag accepts only strings as second parameter");var n=document.createElement("style");this._nonce&&n.setAttribute("nonce",this._nonce),n.type="text/css",n.setAttribute("data-"+e,""),t&&n.appendChild(document.createTextNode(t));var r=document.head||document.getElementsByTagName("head")[0];return i?r.insertBefore(n,i):r.appendChild(n),n},t=[{key:"length",get:function(){return this._rulesCount}}],function(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}(e.prototype,t),e}();function l(e,t){if(!e)throw Error("StyleSheet: "+t+".")}var u=function(e){for(var t=5381,i=e.length;i;)t=33*t^e.charCodeAt(--i);return t>>>0},d={};function c(e,t){if(!t)return"jsx-"+e;var i=String(t),n=e+i;return d[n]||(d[n]="jsx-"+u(e+"-"+i)),d[n]}function h(e,t){var i=e+(t=t.replace(/\/style/gi,"\\/style"));return d[i]||(d[i]=t.replace(/__jsx-style-dynamic-selector/g,e)),d[i]}var p=function(){function e(e){var t=void 0===e?{}:e,i=t.styleSheet,n=void 0===i?null:i,r=t.optimizeForSpeed,s=void 0!==r&&r;this._sheet=n||new o({name:"styled-jsx",optimizeForSpeed:s}),this._sheet.inject(),n&&"boolean"==typeof s&&(this._sheet.setOptimizeForSpeed(s),this._optimizeForSpeed=this._sheet.isOptimizeForSpeed()),this._fromServer=void 0,this._indices={},this._instancesCounts={}}var t=e.prototype;return t.add=function(e){var t=this;void 0===this._optimizeForSpeed&&(this._optimizeForSpeed=Array.isArray(e.children),this._sheet.setOptimizeForSpeed(this._optimizeForSpeed),this._optimizeForSpeed=this._sheet.isOptimizeForSpeed());var i=this.getIdAndRules(e),n=i.styleId,r=i.rules;if(n in this._instancesCounts){this._instancesCounts[n]+=1;return}var s=r.map(function(e){return t._sheet.insertRule(e)}).filter(function(e){return -1!==e});this._indices[n]=s,this._instancesCounts[n]=1},t.remove=function(e){var t=this,i=this.getIdAndRules(e).styleId;if(function(e,t){if(!e)throw Error("StyleSheetRegistry: "+t+".")}(i in this._instancesCounts,"styleId: `"+i+"` not found"),this._instancesCounts[i]-=1,this._instancesCounts[i]<1){var n=this._fromServer&&this._fromServer[i];n?(n.parentNode.removeChild(n),delete this._fromServer[i]):(this._indices[i].forEach(function(e){return t._sheet.deleteRule(e)}),delete this._indices[i]),delete this._instancesCounts[i]}},t.update=function(e,t){this.add(t),this.remove(e)},t.flush=function(){this._sheet.flush(),this._sheet.inject(),this._fromServer=void 0,this._indices={},this._instancesCounts={}},t.cssRules=function(){var e=this,t=this._fromServer?Object.keys(this._fromServer).map(function(t){return[t,e._fromServer[t]]}):[],i=this._sheet.cssRules();return t.concat(Object.keys(this._indices).map(function(t){return[t,e._indices[t].map(function(e){return i[e].cssText}).join(e._optimizeForSpeed?"":"\n")]}).filter(function(e){return!!e[1]}))},t.styles=function(e){var t,i;return t=this.cssRules(),void 0===(i=e)&&(i={}),t.map(function(e){var t=e[0],n=e[1];return r.default.createElement("style",{id:"__"+t,key:"__"+t,nonce:i.nonce?i.nonce:void 0,dangerouslySetInnerHTML:{__html:n}})})},t.getIdAndRules=function(e){var t=e.children,i=e.dynamic,n=e.id;if(i){var r=c(n,i);return{styleId:r,rules:Array.isArray(t)?t.map(function(e){return h(r,e)}):[h(r,t)]}}return{styleId:c(n),rules:Array.isArray(t)?t:[t]}},t.selectFromServer=function(){return Array.prototype.slice.call(document.querySelectorAll('[id^="__jsx-"]')).reduce(function(e,t){return e[t.id.slice(2)]=t,e},{})},e}(),m=n.createContext(null);m.displayName="StyleSheetContext";r.default.useInsertionEffect||r.default.useLayoutEffect;var f=void 0;function g(e){var t=f||n.useContext(m);return t&&t.add(e),null}g.dynamic=function(e){return e.map(function(e){return c(e[0],e[1])}).join(" ")},t.style=g},6180:(e,t,i)=>{"use strict";e.exports=i(5913).style},6341:(e,t,i)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var i in t)Object.defineProperty(e,i,{enumerable:!0,get:t[i]})}(t,{getPreviouslyRevalidatedTags:function(){return y},getUtils:function(){return g},interpolateDynamicPath:function(){return m},normalizeDynamicRouteParams:function(){return f},normalizeVercelUrl:function(){return p}});let n=i(9551),r=i(1959),s=i(2437),a=i(4396),o=i(8034),l=i(5526),u=i(2887),d=i(4722),c=i(6143),h=i(7912);function p(e,t,i){let r=(0,n.parse)(e.url,!0);for(let e of(delete r.search,Object.keys(r.query))){let n=e!==c.NEXT_QUERY_PARAM_PREFIX&&e.startsWith(c.NEXT_QUERY_PARAM_PREFIX),s=e!==c.NEXT_INTERCEPTION_MARKER_PREFIX&&e.startsWith(c.NEXT_INTERCEPTION_MARKER_PREFIX);(n||s||t.includes(e)||i&&Object.keys(i.groups).includes(e))&&delete r.query[e]}e.url=(0,n.format)(r)}function m(e,t,i){if(!i)return e;for(let n of Object.keys(i.groups)){let r,{optional:s,repeat:a}=i.groups[n],o=`[${a?"...":""}${n}]`;s&&(o=`[${o}]`);let l=t[n];r=Array.isArray(l)?l.map(e=>e&&encodeURIComponent(e)).join("/"):l?encodeURIComponent(l):"",e=e.replaceAll(o,r)}return e}function f(e,t,i,n){let r={};for(let s of Object.keys(t.groups)){let a=e[s];"string"==typeof a?a=(0,d.normalizeRscURL)(a):Array.isArray(a)&&(a=a.map(d.normalizeRscURL));let o=i[s],l=t.groups[s].optional;if((Array.isArray(o)?o.some(e=>Array.isArray(a)?a.some(t=>t.includes(e)):null==a?void 0:a.includes(e)):null==a?void 0:a.includes(o))||void 0===a&&!(l&&n))return{params:{},hasValidParams:!1};l&&(!a||Array.isArray(a)&&1===a.length&&("index"===a[0]||a[0]===`[[...${s}]]`))&&(a=void 0,delete e[s]),a&&"string"==typeof a&&t.groups[s].repeat&&(a=a.split("/")),a&&(r[s]=a)}return{params:r,hasValidParams:!0}}function g({page:e,i18n:t,basePath:i,rewrites:n,pageIsDynamic:d,trailingSlash:c,caseSensitive:g}){let y,v,x;return d&&(y=(0,a.getNamedRouteRegex)(e,{prefixRouteKeys:!1}),x=(v=(0,o.getRouteMatcher)(y))(e)),{handleRewrites:function(a,o){let h={},p=o.pathname,m=n=>{let u=(0,s.getPathMatch)(n.source+(c?"(/)?":""),{removeUnnamedParams:!0,strict:!0,sensitive:!!g});if(!o.pathname)return!1;let m=u(o.pathname);if((n.has||n.missing)&&m){let e=(0,l.matchHas)(a,o.query,n.has,n.missing);e?Object.assign(m,e):m=!1}if(m){let{parsedDestination:s,destQuery:a}=(0,l.prepareDestination)({appendParamsToQuery:!0,destination:n.destination,params:m,query:o.query});if(s.protocol)return!0;if(Object.assign(h,a,m),Object.assign(o.query,s.query),delete s.query,Object.assign(o,s),!(p=o.pathname))return!1;if(i&&(p=p.replace(RegExp(`^${i}`),"")||"/"),t){let e=(0,r.normalizeLocalePath)(p,t.locales);p=e.pathname,o.query.nextInternalLocale=e.detectedLocale||m.nextInternalLocale}if(p===e)return!0;if(d&&v){let e=v(p);if(e)return o.query={...o.query,...e},!0}}return!1};for(let e of n.beforeFiles||[])m(e);if(p!==e){let t=!1;for(let e of n.afterFiles||[])if(t=m(e))break;if(!t&&!(()=>{let t=(0,u.removeTrailingSlash)(p||"");return t===(0,u.removeTrailingSlash)(e)||(null==v?void 0:v(t))})()){for(let e of n.fallback||[])if(t=m(e))break}}return h},defaultRouteRegex:y,dynamicRouteMatcher:v,defaultRouteMatches:x,getParamsFromRouteMatches:function(e){if(!y)return null;let{groups:t,routeKeys:i}=y,n=(0,o.getRouteMatcher)({re:{exec:e=>{let n=Object.fromEntries(new URLSearchParams(e));for(let[e,t]of Object.entries(n)){let i=(0,h.normalizeNextQueryParam)(e);i&&(n[i]=t,delete n[e])}let r={};for(let e of Object.keys(i)){let s=i[e];if(!s)continue;let a=t[s],o=n[e];if(!a.optional&&!o)return null;r[a.pos]=o}return r}},groups:t})(e);return n||null},normalizeDynamicRouteParams:(e,t)=>y&&x?f(e,y,x,t):{params:{},hasValidParams:!1},normalizeVercelUrl:(e,t)=>p(e,t,y),interpolateDynamicPath:(e,t)=>m(e,t,y)}}function y(e,t){return"string"==typeof e[c.NEXT_CACHE_REVALIDATED_TAGS_HEADER]&&e[c.NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER]===t?e[c.NEXT_CACHE_REVALIDATED_TAGS_HEADER].split(","):[]}},6397:()=>{},6415:e=>{(()=>{"use strict";"undefined"!=typeof __nccwpck_require__&&(__nccwpck_require__.ab=__dirname+"/");var t={};(()=>{t.parse=function(t,i){if("string"!=typeof t)throw TypeError("argument str must be a string");for(var r={},s=t.split(n),a=(i||{}).decode||e,o=0;o<s.length;o++){var l=s[o],u=l.indexOf("=");if(!(u<0)){var d=l.substr(0,u).trim(),c=l.substr(++u,l.length).trim();'"'==c[0]&&(c=c.slice(1,-1)),void 0==r[d]&&(r[d]=function(e,t){try{return t(e)}catch(t){return e}}(c,a))}}return r},t.serialize=function(e,t,n){var s=n||{},a=s.encode||i;if("function"!=typeof a)throw TypeError("option encode is invalid");if(!r.test(e))throw TypeError("argument name is invalid");var o=a(t);if(o&&!r.test(o))throw TypeError("argument val is invalid");var l=e+"="+o;if(null!=s.maxAge){var u=s.maxAge-0;if(isNaN(u)||!isFinite(u))throw TypeError("option maxAge is invalid");l+="; Max-Age="+Math.floor(u)}if(s.domain){if(!r.test(s.domain))throw TypeError("option domain is invalid");l+="; Domain="+s.domain}if(s.path){if(!r.test(s.path))throw TypeError("option path is invalid");l+="; Path="+s.path}if(s.expires){if("function"!=typeof s.expires.toUTCString)throw TypeError("option expires is invalid");l+="; Expires="+s.expires.toUTCString()}if(s.httpOnly&&(l+="; HttpOnly"),s.secure&&(l+="; Secure"),s.sameSite)switch("string"==typeof s.sameSite?s.sameSite.toLowerCase():s.sameSite){case!0:case"strict":l+="; SameSite=Strict";break;case"lax":l+="; SameSite=Lax";break;case"none":l+="; SameSite=None";break;default:throw TypeError("option sameSite is invalid")}return l};var e=decodeURIComponent,i=encodeURIComponent,n=/; */,r=/^[\u0009\u0020-\u007e\u0080-\u00ff]+$/})(),e.exports=t})()},6533:(e,t,i)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"Image",{enumerable:!0,get:function(){return b}});let n=i(4985),r=i(740),s=i(687),a=r._(i(3210)),o=n._(i(1215)),l=n._(i(512)),u=i(4953),d=i(2756),c=i(7903);i(148);let h=i(9148),p=n._(i(1933)),m=i(3038),f={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1};function g(e,t,i,n,r,s,a){let o=null==e?void 0:e.src;e&&e["data-loaded-src"]!==o&&(e["data-loaded-src"]=o,("decode"in e?e.decode():Promise.resolve()).catch(()=>{}).then(()=>{if(e.parentElement&&e.isConnected){if("empty"!==t&&r(!0),null==i?void 0:i.current){let t=new Event("load");Object.defineProperty(t,"target",{writable:!1,value:e});let n=!1,r=!1;i.current({...t,nativeEvent:t,currentTarget:e,target:e,isDefaultPrevented:()=>n,isPropagationStopped:()=>r,persist:()=>{},preventDefault:()=>{n=!0,t.preventDefault()},stopPropagation:()=>{r=!0,t.stopPropagation()}})}(null==n?void 0:n.current)&&n.current(e)}}))}function y(e){return a.use?{fetchPriority:e}:{fetchpriority:e}}globalThis.__NEXT_IMAGE_IMPORTED=!0;let v=(0,a.forwardRef)((e,t)=>{let{src:i,srcSet:n,sizes:r,height:o,width:l,decoding:u,className:d,style:c,fetchPriority:h,placeholder:p,loading:f,unoptimized:v,fill:x,onLoadRef:b,onLoadingCompleteRef:w,setBlurComplete:j,setShowAltText:P,sizesInput:S,onLoad:E,onError:T,...A}=e,R=(0,a.useCallback)(e=>{e&&(T&&(e.src=e.src),e.complete&&g(e,p,b,w,j,v,S))},[i,p,b,w,j,T,v,S]),k=(0,m.useMergedRef)(t,R);return(0,s.jsx)("img",{...A,...y(h),loading:f,width:l,height:o,decoding:u,"data-nimg":x?"fill":"1",className:d,style:c,sizes:r,srcSet:n,src:i,ref:k,onLoad:e=>{g(e.currentTarget,p,b,w,j,v,S)},onError:e=>{P(!0),"empty"!==p&&j(!0),T&&T(e)}})});function x(e){let{isAppRouter:t,imgAttributes:i}=e,n={as:"image",imageSrcSet:i.srcSet,imageSizes:i.sizes,crossOrigin:i.crossOrigin,referrerPolicy:i.referrerPolicy,...y(i.fetchPriority)};return t&&o.default.preload?(o.default.preload(i.src,n),null):(0,s.jsx)(l.default,{children:(0,s.jsx)("link",{rel:"preload",href:i.srcSet?void 0:i.src,...n},"__nimg-"+i.src+i.srcSet+i.sizes)})}let b=(0,a.forwardRef)((e,t)=>{let i=(0,a.useContext)(h.RouterContext),n=(0,a.useContext)(c.ImageConfigContext),r=(0,a.useMemo)(()=>{var e;let t=f||n||d.imageConfigDefault,i=[...t.deviceSizes,...t.imageSizes].sort((e,t)=>e-t),r=t.deviceSizes.sort((e,t)=>e-t),s=null==(e=t.qualities)?void 0:e.sort((e,t)=>e-t);return{...t,allSizes:i,deviceSizes:r,qualities:s}},[n]),{onLoad:o,onLoadingComplete:l}=e,m=(0,a.useRef)(o);(0,a.useEffect)(()=>{m.current=o},[o]);let g=(0,a.useRef)(l);(0,a.useEffect)(()=>{g.current=l},[l]);let[y,b]=(0,a.useState)(!1),[w,j]=(0,a.useState)(!1),{props:P,meta:S}=(0,u.getImgProps)(e,{defaultLoader:p.default,imgConf:r,blurComplete:y,showAltText:w});return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(v,{...P,unoptimized:S.unoptimized,placeholder:S.placeholder,fill:S.fill,onLoadRef:m,onLoadingCompleteRef:g,setBlurComplete:b,setShowAltText:j,sizesInput:e.sizes,ref:t}),S.priority?(0,s.jsx)(x,{isAppRouter:!i,imgAttributes:P}):null]})});("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6759:(e,t,i)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"parseUrl",{enumerable:!0,get:function(){return s}});let n=i(2785),r=i(3736);function s(e){if(e.startsWith("/"))return(0,r.parseRelativeUrl)(e);let t=new URL(e);return{hash:t.hash,hostname:t.hostname,href:t.href,pathname:t.pathname,port:t.port,protocol:t.protocol,query:(0,n.searchParamsToUrlQuery)(t.searchParams),search:t.search}}},7363:(e,t,i)=>{Promise.resolve().then(i.t.bind(i,6346,23)),Promise.resolve().then(i.t.bind(i,7924,23)),Promise.resolve().then(i.t.bind(i,5656,23)),Promise.resolve().then(i.t.bind(i,99,23)),Promise.resolve().then(i.t.bind(i,8243,23)),Promise.resolve().then(i.t.bind(i,8827,23)),Promise.resolve().then(i.t.bind(i,2763,23)),Promise.resolve().then(i.t.bind(i,7173,23))},7755:(e,t,i)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return a}});let n=i(3210),r=()=>{},s=()=>{};function a(e){var t;let{headManager:i,reduceComponentsToState:a}=e;function o(){if(i&&i.mountedInstances){let t=n.Children.toArray(Array.from(i.mountedInstances).filter(Boolean));i.updateHead(a(t,e))}}return null==i||null==(t=i.mountedInstances)||t.add(e.children),o(),r(()=>{var t;return null==i||null==(t=i.mountedInstances)||t.add(e.children),()=>{var t;null==i||null==(t=i.mountedInstances)||t.delete(e.children)}}),r(()=>(i&&(i._pendingUpdate=o),()=>{i&&(i._pendingUpdate=o)})),s(()=>(i&&i._pendingUpdate&&(i._pendingUpdate(),i._pendingUpdate=null),()=>{i&&i._pendingUpdate&&(i._pendingUpdate(),i._pendingUpdate=null)})),null}},7903:(e,t,i)=>{"use strict";e.exports=i(4041).vendored.contexts.ImageConfigContext},8034:(e,t,i)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getRouteMatcher",{enumerable:!0,get:function(){return r}});let n=i(4827);function r(e){let{re:t,groups:i}=e;return e=>{let r=t.exec(e);if(!r)return!1;let s=e=>{try{return decodeURIComponent(e)}catch(e){throw Object.defineProperty(new n.DecodeError("failed to decode param"),"__NEXT_ERROR_CODE",{value:"E528",enumerable:!1,configurable:!0})}},a={};for(let[e,t]of Object.entries(i)){let i=r[t.pos];void 0!==i&&(t.repeat?a[e]=i.split("/").map(e=>s(e)):a[e]=s(i))}return a}}},8212:(e,t,i)=>{"use strict";function n(e){return function(){let{cookie:t}=e;if(!t)return{};let{parse:n}=i(6415);return n(Array.isArray(t)?t.join("; "):t)}}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getCookieParser",{enumerable:!0,get:function(){return n}})},8304:(e,t,i)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var i in t)Object.defineProperty(e,i,{enumerable:!0,get:t[i]})}(t,{DEFAULT_METADATA_ROUTE_EXTENSIONS:function(){return o},STATIC_METADATA_IMAGES:function(){return a},getExtensionRegexString:function(){return l},isMetadataPage:function(){return c},isMetadataRoute:function(){return h},isMetadataRouteFile:function(){return u},isStaticMetadataRoute:function(){return d}});let n=i(2958),r=i(4722),s=i(554),a={icon:{filename:"icon",extensions:["ico","jpg","jpeg","png","svg"]},apple:{filename:"apple-icon",extensions:["jpg","jpeg","png"]},favicon:{filename:"favicon",extensions:["ico"]},openGraph:{filename:"opengraph-image",extensions:["jpg","jpeg","png","gif"]},twitter:{filename:"twitter-image",extensions:["jpg","jpeg","png","gif"]}},o=["js","jsx","ts","tsx"],l=(e,t)=>t&&0!==t.length?`(?:\\.(${e.join("|")})|(\\.(${t.join("|")})))`:`(\\.(?:${e.join("|")}))`;function u(e,t,i){let r=(i?"":"?")+"$",s=`\\d?${i?"":"(-\\w{6})?"}`,o=[RegExp(`^[\\\\/]robots${l(t.concat("txt"),null)}${r}`),RegExp(`^[\\\\/]manifest${l(t.concat("webmanifest","json"),null)}${r}`),RegExp("^[\\\\/]favicon\\.ico$"),RegExp(`[\\\\/]sitemap${l(["xml"],t)}${r}`),RegExp(`[\\\\/]${a.icon.filename}${s}${l(a.icon.extensions,t)}${r}`),RegExp(`[\\\\/]${a.apple.filename}${s}${l(a.apple.extensions,t)}${r}`),RegExp(`[\\\\/]${a.openGraph.filename}${s}${l(a.openGraph.extensions,t)}${r}`),RegExp(`[\\\\/]${a.twitter.filename}${s}${l(a.twitter.extensions,t)}${r}`)],u=(0,n.normalizePathSep)(e);return o.some(e=>e.test(u))}function d(e){let t=e.replace(/\/route$/,"");return(0,s.isAppRouteRoute)(e)&&u(t,[],!0)&&"/robots.txt"!==t&&"/manifest.webmanifest"!==t&&!t.endsWith("/sitemap.xml")}function c(e){return!(0,s.isAppRouteRoute)(e)&&u(e,[],!1)}function h(e){let t=(0,r.normalizeAppPath)(e).replace(/^\/?app\//,"").replace("/[__metadata_id__]","").replace(/\/route$/,"");return"/"!==t[0]&&(t="/"+t),(0,s.isAppRouteRoute)(e)&&u(t,[],!1)}},8780:(e,t,i)=>{"use strict";let n;i.d(t,{default:()=>sX});var r,s,a=i(687);function o(e){return null!==e&&"object"==typeof e&&"function"==typeof e.start}function l(e){let t=[{},{}];return e?.values.forEach((e,i)=>{t[0][i]=e.get(),t[1][i]=e.getVelocity()}),t}function u(e,t,i,n){if("function"==typeof t){let[r,s]=l(n);t=t(void 0!==i?i:e.custom,r,s)}if("string"==typeof t&&(t=e.variants&&e.variants[t]),"function"==typeof t){let[r,s]=l(n);t=t(void 0!==i?i:e.custom,r,s)}return t}function d(e,t,i){let n=e.getProps();return u(n,t,void 0!==i?i:n.custom,e)}function c(e,t){return e?.[t]??e?.default??e}let h=e=>e,p={},m=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"],f={value:null,addProjectionMetrics:null};function g(e,t){let i=!1,n=!0,r={delta:0,timestamp:0,isProcessing:!1},s=()=>i=!0,a=m.reduce((e,i)=>(e[i]=function(e,t){let i=new Set,n=new Set,r=!1,s=!1,a=new WeakSet,o={delta:0,timestamp:0,isProcessing:!1},l=0;function u(t){a.has(t)&&(d.schedule(t),e()),l++,t(o)}let d={schedule:(e,t=!1,s=!1)=>{let o=s&&r?i:n;return t&&a.add(e),o.has(e)||o.add(e),e},cancel:e=>{n.delete(e),a.delete(e)},process:e=>{if(o=e,r){s=!0;return}r=!0,[i,n]=[n,i],i.forEach(u),t&&f.value&&f.value.frameloop[t].push(l),l=0,i.clear(),r=!1,s&&(s=!1,d.process(e))}};return d}(s,t?i:void 0),e),{}),{setup:o,read:l,resolveKeyframes:u,preUpdate:d,update:c,preRender:h,render:g,postRender:y}=a,v=()=>{let s=p.useManualTiming?r.timestamp:performance.now();i=!1,p.useManualTiming||(r.delta=n?1e3/60:Math.max(Math.min(s-r.timestamp,40),1)),r.timestamp=s,r.isProcessing=!0,o.process(r),l.process(r),u.process(r),d.process(r),c.process(r),h.process(r),g.process(r),y.process(r),r.isProcessing=!1,i&&t&&(n=!1,e(v))},x=()=>{i=!0,n=!0,r.isProcessing||e(v)};return{schedule:m.reduce((e,t)=>{let n=a[t];return e[t]=(e,t=!1,r=!1)=>(i||x(),n.schedule(e,t,r)),e},{}),cancel:e=>{for(let t=0;t<m.length;t++)a[m[t]].cancel(e)},state:r,steps:a}}let{schedule:y,cancel:v,state:x,steps:b}=g("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:h,!0),w=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],j=new Set(w),P=new Set(["width","height","top","left","right","bottom",...w]);function S(e,t){-1===e.indexOf(t)&&e.push(t)}function E(e,t){let i=e.indexOf(t);i>-1&&e.splice(i,1)}class T{constructor(){this.subscriptions=[]}add(e){return S(this.subscriptions,e),()=>E(this.subscriptions,e)}notify(e,t,i){let n=this.subscriptions.length;if(n)if(1===n)this.subscriptions[0](e,t,i);else for(let r=0;r<n;r++){let n=this.subscriptions[r];n&&n(e,t,i)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}function A(){n=void 0}let R={now:()=>(void 0===n&&R.set(x.isProcessing||p.useManualTiming?x.timestamp:performance.now()),n),set:e=>{n=e,queueMicrotask(A)}},k=e=>!isNaN(parseFloat(e)),C={current:void 0};class _{constructor(e,t={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=(e,t=!0)=>{let i=R.now();if(this.updatedAt!==i&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(e),this.current!==this.prev&&(this.events.change?.notify(this.current),this.dependents))for(let e of this.dependents)e.dirty();t&&this.events.renderRequest?.notify(this.current)},this.hasAnimated=!1,this.setCurrent(e),this.owner=t.owner}setCurrent(e){this.current=e,this.updatedAt=R.now(),null===this.canTrackVelocity&&void 0!==e&&(this.canTrackVelocity=k(this.current))}setPrevFrameValue(e=this.current){this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt}onChange(e){return this.on("change",e)}on(e,t){this.events[e]||(this.events[e]=new T);let i=this.events[e].add(t);return"change"===e?()=>{i(),y.read(()=>{this.events.change.getSize()||this.stop()})}:i}clearListeners(){for(let e in this.events)this.events[e].clear()}attach(e,t){this.passiveEffect=e,this.stopPassiveEffect=t}set(e,t=!0){t&&this.passiveEffect?this.passiveEffect(e,this.updateAndNotify):this.updateAndNotify(e,t)}setWithVelocity(e,t,i){this.set(t),this.prev=void 0,this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt-i}jump(e,t=!0){this.updateAndNotify(e),this.prev=e,this.prevUpdatedAt=this.prevFrameValue=void 0,t&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){this.events.change?.notify(this.current)}addDependent(e){this.dependents||(this.dependents=new Set),this.dependents.add(e)}removeDependent(e){this.dependents&&this.dependents.delete(e)}get(){return C.current&&C.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){var e;let t=R.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||t-this.updatedAt>30)return 0;let i=Math.min(this.updatedAt-this.prevUpdatedAt,30);return e=parseFloat(this.current)-parseFloat(this.prevFrameValue),i?1e3/i*e:0}start(e){return this.stop(),new Promise(t=>{this.hasAnimated=!0,this.animation=e(t),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.dependents?.clear(),this.events.destroy?.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function M(e,t){return new _(e,t)}let N=e=>Array.isArray(e),D=e=>!!(e&&e.getVelocity);function O(e,t){let i=e.getValue("willChange");if(D(i)&&i.add)return i.add(t);if(!i&&p.WillChange){let i=new p.WillChange("auto");e.addValue("willChange",i),i.add(t)}}let V=e=>e.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),I="data-"+V("framerAppearId"),F=(e,t)=>i=>t(e(i)),L=(...e)=>e.reduce(F),$=(e,t,i)=>i>t?t:i<e?e:i,B=e=>1e3*e,U=e=>e/1e3,z={layout:0,mainThread:0,waapi:0},W=()=>{},H=()=>{},q=e=>t=>"string"==typeof t&&t.startsWith(e),X=q("--"),K=q("var(--"),G=e=>!!K(e)&&Y.test(e.split("/*")[0].trim()),Y=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,Z={test:e=>"number"==typeof e,parse:parseFloat,transform:e=>e},J={...Z,transform:e=>$(0,1,e)},Q={...Z,default:1},ee=e=>Math.round(1e5*e)/1e5,et=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu,ei=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,en=(e,t)=>i=>!!("string"==typeof i&&ei.test(i)&&i.startsWith(e)||t&&null!=i&&Object.prototype.hasOwnProperty.call(i,t)),er=(e,t,i)=>n=>{if("string"!=typeof n)return n;let[r,s,a,o]=n.match(et);return{[e]:parseFloat(r),[t]:parseFloat(s),[i]:parseFloat(a),alpha:void 0!==o?parseFloat(o):1}},es=e=>$(0,255,e),ea={...Z,transform:e=>Math.round(es(e))},eo={test:en("rgb","red"),parse:er("red","green","blue"),transform:({red:e,green:t,blue:i,alpha:n=1})=>"rgba("+ea.transform(e)+", "+ea.transform(t)+", "+ea.transform(i)+", "+ee(J.transform(n))+")"},el={test:en("#"),parse:function(e){let t="",i="",n="",r="";return e.length>5?(t=e.substring(1,3),i=e.substring(3,5),n=e.substring(5,7),r=e.substring(7,9)):(t=e.substring(1,2),i=e.substring(2,3),n=e.substring(3,4),r=e.substring(4,5),t+=t,i+=i,n+=n,r+=r),{red:parseInt(t,16),green:parseInt(i,16),blue:parseInt(n,16),alpha:r?parseInt(r,16)/255:1}},transform:eo.transform},eu=e=>({test:t=>"string"==typeof t&&t.endsWith(e)&&1===t.split(" ").length,parse:parseFloat,transform:t=>`${t}${e}`}),ed=eu("deg"),ec=eu("%"),eh=eu("px"),ep=eu("vh"),em=eu("vw"),ef={...ec,parse:e=>ec.parse(e)/100,transform:e=>ec.transform(100*e)},eg={test:en("hsl","hue"),parse:er("hue","saturation","lightness"),transform:({hue:e,saturation:t,lightness:i,alpha:n=1})=>"hsla("+Math.round(e)+", "+ec.transform(ee(t))+", "+ec.transform(ee(i))+", "+ee(J.transform(n))+")"},ey={test:e=>eo.test(e)||el.test(e)||eg.test(e),parse:e=>eo.test(e)?eo.parse(e):eg.test(e)?eg.parse(e):el.parse(e),transform:e=>"string"==typeof e?e:e.hasOwnProperty("red")?eo.transform(e):eg.transform(e),getAnimatableNone:e=>{let t=ey.parse(e);return t.alpha=0,ey.transform(t)}},ev=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu,ex="number",eb="color",ew=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function ej(e){let t=e.toString(),i=[],n={color:[],number:[],var:[]},r=[],s=0,a=t.replace(ew,e=>(ey.test(e)?(n.color.push(s),r.push(eb),i.push(ey.parse(e))):e.startsWith("var(")?(n.var.push(s),r.push("var"),i.push(e)):(n.number.push(s),r.push(ex),i.push(parseFloat(e))),++s,"${}")).split("${}");return{values:i,split:a,indexes:n,types:r}}function eP(e){return ej(e).values}function eS(e){let{split:t,types:i}=ej(e),n=t.length;return e=>{let r="";for(let s=0;s<n;s++)if(r+=t[s],void 0!==e[s]){let t=i[s];t===ex?r+=ee(e[s]):t===eb?r+=ey.transform(e[s]):r+=e[s]}return r}}let eE=e=>"number"==typeof e?0:ey.test(e)?ey.getAnimatableNone(e):e,eT={test:function(e){return isNaN(e)&&"string"==typeof e&&(e.match(et)?.length||0)+(e.match(ev)?.length||0)>0},parse:eP,createTransformer:eS,getAnimatableNone:function(e){let t=eP(e);return eS(e)(t.map(eE))}};function eA(e,t,i){return(i<0&&(i+=1),i>1&&(i-=1),i<1/6)?e+(t-e)*6*i:i<.5?t:i<2/3?e+(t-e)*(2/3-i)*6:e}function eR(e,t){return i=>i>0?t:e}let ek=(e,t,i)=>e+(t-e)*i,eC=(e,t,i)=>{let n=e*e,r=i*(t*t-n)+n;return r<0?0:Math.sqrt(r)},e_=[el,eo,eg],eM=e=>e_.find(t=>t.test(e));function eN(e){let t=eM(e);if(W(!!t,`'${e}' is not an animatable color. Use the equivalent color code instead.`),!t)return!1;let i=t.parse(e);return t===eg&&(i=function({hue:e,saturation:t,lightness:i,alpha:n}){e/=360,i/=100;let r=0,s=0,a=0;if(t/=100){let n=i<.5?i*(1+t):i+t-i*t,o=2*i-n;r=eA(o,n,e+1/3),s=eA(o,n,e),a=eA(o,n,e-1/3)}else r=s=a=i;return{red:Math.round(255*r),green:Math.round(255*s),blue:Math.round(255*a),alpha:n}}(i)),i}let eD=(e,t)=>{let i=eN(e),n=eN(t);if(!i||!n)return eR(e,t);let r={...i};return e=>(r.red=eC(i.red,n.red,e),r.green=eC(i.green,n.green,e),r.blue=eC(i.blue,n.blue,e),r.alpha=ek(i.alpha,n.alpha,e),eo.transform(r))},eO=new Set(["none","hidden"]);function eV(e,t){return i=>ek(e,t,i)}function eI(e){return"number"==typeof e?eV:"string"==typeof e?G(e)?eR:ey.test(e)?eD:e$:Array.isArray(e)?eF:"object"==typeof e?ey.test(e)?eD:eL:eR}function eF(e,t){let i=[...e],n=i.length,r=e.map((e,i)=>eI(e)(e,t[i]));return e=>{for(let t=0;t<n;t++)i[t]=r[t](e);return i}}function eL(e,t){let i={...e,...t},n={};for(let r in i)void 0!==e[r]&&void 0!==t[r]&&(n[r]=eI(e[r])(e[r],t[r]));return e=>{for(let t in n)i[t]=n[t](e);return i}}let e$=(e,t)=>{let i=eT.createTransformer(t),n=ej(e),r=ej(t);return n.indexes.var.length===r.indexes.var.length&&n.indexes.color.length===r.indexes.color.length&&n.indexes.number.length>=r.indexes.number.length?eO.has(e)&&!r.values.length||eO.has(t)&&!n.values.length?function(e,t){return eO.has(e)?i=>i<=0?e:t:i=>i>=1?t:e}(e,t):L(eF(function(e,t){let i=[],n={color:0,var:0,number:0};for(let r=0;r<t.values.length;r++){let s=t.types[r],a=e.indexes[s][n[s]],o=e.values[a]??0;i[r]=o,n[s]++}return i}(n,r),r.values),i):(W(!0,`Complex values '${e}' and '${t}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`),eR(e,t))};function eB(e,t,i){return"number"==typeof e&&"number"==typeof t&&"number"==typeof i?ek(e,t,i):eI(e)(e,t)}let eU=e=>{let t=({timestamp:t})=>e(t);return{start:(e=!0)=>y.update(t,e),stop:()=>v(t),now:()=>x.isProcessing?x.timestamp:R.now()}},ez=(e,t,i=10)=>{let n="",r=Math.max(Math.round(t/i),2);for(let t=0;t<r;t++)n+=Math.round(1e4*e(t/(r-1)))/1e4+", ";return`linear(${n.substring(0,n.length-2)})`};function eW(e){let t=0,i=e.next(t);for(;!i.done&&t<2e4;)t+=50,i=e.next(t);return t>=2e4?1/0:t}function eH(e,t,i){var n,r;let s=Math.max(t-5,0);return n=i-e(s),(r=t-s)?1e3/r*n:0}let eq={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1};function eX(e,t){return e*Math.sqrt(1-t*t)}let eK=["duration","bounce"],eG=["stiffness","damping","mass"];function eY(e,t){return t.some(t=>void 0!==e[t])}function eZ(e=eq.visualDuration,t=eq.bounce){let i,n="object"!=typeof e?{visualDuration:e,keyframes:[0,1],bounce:t}:e,{restSpeed:r,restDelta:s}=n,a=n.keyframes[0],o=n.keyframes[n.keyframes.length-1],l={done:!1,value:a},{stiffness:u,damping:d,mass:c,duration:h,velocity:p,isResolvedFromDuration:m}=function(e){let t={velocity:eq.velocity,stiffness:eq.stiffness,damping:eq.damping,mass:eq.mass,isResolvedFromDuration:!1,...e};if(!eY(e,eG)&&eY(e,eK))if(e.visualDuration){let i=2*Math.PI/(1.2*e.visualDuration),n=i*i,r=2*$(.05,1,1-(e.bounce||0))*Math.sqrt(n);t={...t,mass:eq.mass,stiffness:n,damping:r}}else{let i=function({duration:e=eq.duration,bounce:t=eq.bounce,velocity:i=eq.velocity,mass:n=eq.mass}){let r,s;W(e<=B(eq.maxDuration),"Spring duration must be 10 seconds or less");let a=1-t;a=$(eq.minDamping,eq.maxDamping,a),e=$(eq.minDuration,eq.maxDuration,U(e)),a<1?(r=t=>{let n=t*a,r=n*e;return .001-(n-i)/eX(t,a)*Math.exp(-r)},s=t=>{let n=t*a*e,s=Math.pow(a,2)*Math.pow(t,2)*e,o=Math.exp(-n),l=eX(Math.pow(t,2),a);return(n*i+i-s)*o*(-r(t)+.001>0?-1:1)/l}):(r=t=>-.001+Math.exp(-t*e)*((t-i)*e+1),s=t=>e*e*(i-t)*Math.exp(-t*e));let o=function(e,t,i){let n=i;for(let i=1;i<12;i++)n-=e(n)/t(n);return n}(r,s,5/e);if(e=B(e),isNaN(o))return{stiffness:eq.stiffness,damping:eq.damping,duration:e};{let t=Math.pow(o,2)*n;return{stiffness:t,damping:2*a*Math.sqrt(n*t),duration:e}}}(e);(t={...t,...i,mass:eq.mass}).isResolvedFromDuration=!0}return t}({...n,velocity:-U(n.velocity||0)}),f=p||0,g=d/(2*Math.sqrt(u*c)),y=o-a,v=U(Math.sqrt(u/c)),x=5>Math.abs(y);if(r||(r=x?eq.restSpeed.granular:eq.restSpeed.default),s||(s=x?eq.restDelta.granular:eq.restDelta.default),g<1){let e=eX(v,g);i=t=>o-Math.exp(-g*v*t)*((f+g*v*y)/e*Math.sin(e*t)+y*Math.cos(e*t))}else if(1===g)i=e=>o-Math.exp(-v*e)*(y+(f+v*y)*e);else{let e=v*Math.sqrt(g*g-1);i=t=>{let i=Math.exp(-g*v*t),n=Math.min(e*t,300);return o-i*((f+g*v*y)*Math.sinh(n)+e*y*Math.cosh(n))/e}}let b={calculatedDuration:m&&h||null,next:e=>{let t=i(e);if(m)l.done=e>=h;else{let n=0===e?f:0;g<1&&(n=0===e?B(f):eH(i,e,t));let a=Math.abs(o-t)<=s;l.done=Math.abs(n)<=r&&a}return l.value=l.done?o:t,l},toString:()=>{let e=Math.min(eW(b),2e4),t=ez(t=>b.next(e*t).value,e,30);return e+"ms "+t},toTransition:()=>{}};return b}function eJ({keyframes:e,velocity:t=0,power:i=.8,timeConstant:n=325,bounceDamping:r=10,bounceStiffness:s=500,modifyTarget:a,min:o,max:l,restDelta:u=.5,restSpeed:d}){let c,h,p=e[0],m={done:!1,value:p},f=e=>void 0!==o&&e<o||void 0!==l&&e>l,g=e=>void 0===o?l:void 0===l||Math.abs(o-e)<Math.abs(l-e)?o:l,y=i*t,v=p+y,x=void 0===a?v:a(v);x!==v&&(y=x-p);let b=e=>-y*Math.exp(-e/n),w=e=>x+b(e),j=e=>{let t=b(e),i=w(e);m.done=Math.abs(t)<=u,m.value=m.done?x:i},P=e=>{f(m.value)&&(c=e,h=eZ({keyframes:[m.value,g(m.value)],velocity:eH(w,e,m.value),damping:r,stiffness:s,restDelta:u,restSpeed:d}))};return P(0),{calculatedDuration:null,next:e=>{let t=!1;return(h||void 0!==c||(t=!0,j(e),P(e)),void 0!==c&&e>=c)?h.next(e-c):(t||j(e),m)}}}eZ.applyToOptions=e=>{let t=function(e,t=100,i){let n=i({...e,keyframes:[0,t]}),r=Math.min(eW(n),2e4);return{type:"keyframes",ease:e=>n.next(r*e).value/t,duration:U(r)}}(e,100,eZ);return e.ease=t.ease,e.duration=B(t.duration),e.type="keyframes",e};let eQ=(e,t,i)=>(((1-3*i+3*t)*e+(3*i-6*t))*e+3*t)*e;function e0(e,t,i,n){if(e===t&&i===n)return h;let r=t=>(function(e,t,i,n,r){let s,a,o=0;do(s=eQ(a=t+(i-t)/2,n,r)-e)>0?i=a:t=a;while(Math.abs(s)>1e-7&&++o<12);return a})(t,0,1,e,i);return e=>0===e||1===e?e:eQ(r(e),t,n)}let e1=e0(.42,0,1,1),e2=e0(0,0,.58,1),e3=e0(.42,0,.58,1),e5=e=>Array.isArray(e)&&"number"!=typeof e[0],e4=e=>t=>t<=.5?e(2*t)/2:(2-e(2*(1-t)))/2,e6=e=>t=>1-e(1-t),e8=e0(.33,1.53,.69,.99),e9=e6(e8),e7=e4(e9),te=e=>(e*=2)<1?.5*e9(e):.5*(2-Math.pow(2,-10*(e-1))),tt=e=>1-Math.sin(Math.acos(e)),ti=e6(tt),tn=e4(tt),tr=e=>Array.isArray(e)&&"number"==typeof e[0],ts={linear:h,easeIn:e1,easeInOut:e3,easeOut:e2,circIn:tt,circInOut:tn,circOut:ti,backIn:e9,backInOut:e7,backOut:e8,anticipate:te},ta=e=>"string"==typeof e,to=e=>{if(tr(e)){H(4===e.length,"Cubic bezier arrays must contain four numerical values.");let[t,i,n,r]=e;return e0(t,i,n,r)}return ta(e)?(H(void 0!==ts[e],`Invalid easing type '${e}'`),ts[e]):e},tl=(e,t,i)=>{let n=t-e;return 0===n?1:(i-e)/n};function tu({duration:e=300,keyframes:t,times:i,ease:n="easeInOut"}){var r;let s=e5(n)?n.map(to):to(n),a={done:!1,value:t[0]},o=function(e,t,{clamp:i=!0,ease:n,mixer:r}={}){let s=e.length;if(H(s===t.length,"Both input and output ranges must be the same length"),1===s)return()=>t[0];if(2===s&&t[0]===t[1])return()=>t[1];let a=e[0]===e[1];e[0]>e[s-1]&&(e=[...e].reverse(),t=[...t].reverse());let o=function(e,t,i){let n=[],r=i||p.mix||eB,s=e.length-1;for(let i=0;i<s;i++){let s=r(e[i],e[i+1]);t&&(s=L(Array.isArray(t)?t[i]||h:t,s)),n.push(s)}return n}(t,n,r),l=o.length,u=i=>{if(a&&i<e[0])return t[0];let n=0;if(l>1)for(;n<e.length-2&&!(i<e[n+1]);n++);let r=tl(e[n],e[n+1],i);return o[n](r)};return i?t=>u($(e[0],e[s-1],t)):u}((r=i&&i.length===t.length?i:function(e){let t=[0];return!function(e,t){let i=e[e.length-1];for(let n=1;n<=t;n++){let r=tl(0,t,n);e.push(ek(i,1,r))}}(t,e.length-1),t}(t),r.map(t=>t*e)),t,{ease:Array.isArray(s)?s:t.map(()=>s||e3).splice(0,t.length-1)});return{calculatedDuration:e,next:t=>(a.value=o(t),a.done=t>=e,a)}}let td=e=>null!==e;function tc(e,{repeat:t,repeatType:i="loop"},n,r=1){let s=e.filter(td),a=r<0||t&&"loop"!==i&&t%2==1?0:s.length-1;return a&&void 0!==n?n:s[a]}let th={decay:eJ,inertia:eJ,tween:tu,keyframes:tu,spring:eZ};function tp(e){"string"==typeof e.type&&(e.type=th[e.type])}class tm{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise(e=>{this.resolve=e})}notifyFinished(){this.resolve()}then(e,t){return this.finished.then(e,t)}}let tf=e=>e/100;class tg extends tm{constructor(e){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=()=>{let{motionValue:e}=this.options;e&&e.updatedAt!==R.now()&&this.tick(R.now()),this.isStopped=!0,"idle"!==this.state&&(this.teardown(),this.options.onStop?.())},z.mainThread++,this.options=e,this.initAnimation(),this.play(),!1===e.autoplay&&this.pause()}initAnimation(){let{options:e}=this;tp(e);let{type:t=tu,repeat:i=0,repeatDelay:n=0,repeatType:r,velocity:s=0}=e,{keyframes:a}=e,o=t||tu;o!==tu&&"number"!=typeof a[0]&&(this.mixKeyframes=L(tf,eB(a[0],a[1])),a=[0,100]);let l=o({...e,keyframes:a});"mirror"===r&&(this.mirroredGenerator=o({...e,keyframes:[...a].reverse(),velocity:-s})),null===l.calculatedDuration&&(l.calculatedDuration=eW(l));let{calculatedDuration:u}=l;this.calculatedDuration=u,this.resolvedDuration=u+n,this.totalDuration=this.resolvedDuration*(i+1)-n,this.generator=l}updateTime(e){let t=Math.round(e-this.startTime)*this.playbackSpeed;null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=t}tick(e,t=!1){let{generator:i,totalDuration:n,mixKeyframes:r,mirroredGenerator:s,resolvedDuration:a,calculatedDuration:o}=this;if(null===this.startTime)return i.next(0);let{delay:l=0,keyframes:u,repeat:d,repeatType:c,repeatDelay:h,type:p,onUpdate:m,finalKeyframe:f}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,e):this.speed<0&&(this.startTime=Math.min(e-n/this.speed,this.startTime)),t?this.currentTime=e:this.updateTime(e);let g=this.currentTime-l*(this.playbackSpeed>=0?1:-1),y=this.playbackSpeed>=0?g<0:g>n;this.currentTime=Math.max(g,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=n);let v=this.currentTime,x=i;if(d){let e=Math.min(this.currentTime,n)/a,t=Math.floor(e),i=e%1;!i&&e>=1&&(i=1),1===i&&t--,(t=Math.min(t,d+1))%2&&("reverse"===c?(i=1-i,h&&(i-=h/a)):"mirror"===c&&(x=s)),v=$(0,1,i)*a}let b=y?{done:!1,value:u[0]}:x.next(v);r&&(b.value=r(b.value));let{done:w}=b;y||null===o||(w=this.playbackSpeed>=0?this.currentTime>=n:this.currentTime<=0);let j=null===this.holdTime&&("finished"===this.state||"running"===this.state&&w);return j&&p!==eJ&&(b.value=tc(u,this.options,f,this.speed)),m&&m(b.value),j&&this.finish(),b}then(e,t){return this.finished.then(e,t)}get duration(){return U(this.calculatedDuration)}get time(){return U(this.currentTime)}set time(e){e=B(e),this.currentTime=e,null===this.startTime||null!==this.holdTime||0===this.playbackSpeed?this.holdTime=e:this.driver&&(this.startTime=this.driver.now()-e/this.playbackSpeed),this.driver?.start(!1)}get speed(){return this.playbackSpeed}set speed(e){this.updateTime(R.now());let t=this.playbackSpeed!==e;this.playbackSpeed=e,t&&(this.time=U(this.currentTime))}play(){if(this.isStopped)return;let{driver:e=eU,startTime:t}=this.options;this.driver||(this.driver=e(e=>this.tick(e))),this.options.onPlay?.();let i=this.driver.now();"finished"===this.state?(this.updateFinished(),this.startTime=i):null!==this.holdTime?this.startTime=i-this.holdTime:this.startTime||(this.startTime=t??i),"finished"===this.state&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(R.now()),this.holdTime=this.currentTime}complete(){"running"!==this.state&&this.play(),this.state="finished",this.holdTime=null}finish(){this.notifyFinished(),this.teardown(),this.state="finished",this.options.onComplete?.()}cancel(){this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),this.options.onCancel?.()}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null,z.mainThread--}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(e){return this.startTime=0,this.tick(e,!0)}attachTimeline(e){return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),this.driver?.stop(),e.observe(this)}}let ty=e=>180*e/Math.PI,tv=e=>tb(ty(Math.atan2(e[1],e[0]))),tx={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:e=>(Math.abs(e[0])+Math.abs(e[3]))/2,rotate:tv,rotateZ:tv,skewX:e=>ty(Math.atan(e[1])),skewY:e=>ty(Math.atan(e[2])),skew:e=>(Math.abs(e[1])+Math.abs(e[2]))/2},tb=e=>((e%=360)<0&&(e+=360),e),tw=e=>Math.sqrt(e[0]*e[0]+e[1]*e[1]),tj=e=>Math.sqrt(e[4]*e[4]+e[5]*e[5]),tP={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:tw,scaleY:tj,scale:e=>(tw(e)+tj(e))/2,rotateX:e=>tb(ty(Math.atan2(e[6],e[5]))),rotateY:e=>tb(ty(Math.atan2(-e[2],e[0]))),rotateZ:tv,rotate:tv,skewX:e=>ty(Math.atan(e[4])),skewY:e=>ty(Math.atan(e[1])),skew:e=>(Math.abs(e[1])+Math.abs(e[4]))/2};function tS(e){return+!!e.includes("scale")}function tE(e,t){let i,n;if(!e||"none"===e)return tS(t);let r=e.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);if(r)i=tP,n=r;else{let t=e.match(/^matrix\(([-\d.e\s,]+)\)$/u);i=tx,n=t}if(!n)return tS(t);let s=i[t],a=n[1].split(",").map(tA);return"function"==typeof s?s(a):a[s]}let tT=(e,t)=>{let{transform:i="none"}=getComputedStyle(e);return tE(i,t)};function tA(e){return parseFloat(e.trim())}let tR=e=>e===Z||e===eh,tk=new Set(["x","y","z"]),tC=w.filter(e=>!tk.has(e)),t_={width:({x:e},{paddingLeft:t="0",paddingRight:i="0"})=>e.max-e.min-parseFloat(t)-parseFloat(i),height:({y:e},{paddingTop:t="0",paddingBottom:i="0"})=>e.max-e.min-parseFloat(t)-parseFloat(i),top:(e,{top:t})=>parseFloat(t),left:(e,{left:t})=>parseFloat(t),bottom:({y:e},{top:t})=>parseFloat(t)+(e.max-e.min),right:({x:e},{left:t})=>parseFloat(t)+(e.max-e.min),x:(e,{transform:t})=>tE(t,"x"),y:(e,{transform:t})=>tE(t,"y")};t_.translateX=t_.x,t_.translateY=t_.y;let tM=new Set,tN=!1,tD=!1,tO=!1;function tV(){if(tD){let e=Array.from(tM).filter(e=>e.needsMeasurement),t=new Set(e.map(e=>e.element)),i=new Map;t.forEach(e=>{let t=function(e){let t=[];return tC.forEach(i=>{let n=e.getValue(i);void 0!==n&&(t.push([i,n.get()]),n.set(+!!i.startsWith("scale")))}),t}(e);t.length&&(i.set(e,t),e.render())}),e.forEach(e=>e.measureInitialState()),t.forEach(e=>{e.render();let t=i.get(e);t&&t.forEach(([t,i])=>{e.getValue(t)?.set(i)})}),e.forEach(e=>e.measureEndState()),e.forEach(e=>{void 0!==e.suspendedScrollY&&window.scrollTo(0,e.suspendedScrollY)})}tD=!1,tN=!1,tM.forEach(e=>e.complete(tO)),tM.clear()}function tI(){tM.forEach(e=>{e.readKeyframes(),e.needsMeasurement&&(tD=!0)})}class tF{constructor(e,t,i,n,r,s=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...e],this.onComplete=t,this.name=i,this.motionValue=n,this.element=r,this.isAsync=s}scheduleResolve(){this.state="scheduled",this.isAsync?(tM.add(this),tN||(tN=!0,y.read(tI),y.resolveKeyframes(tV))):(this.readKeyframes(),this.complete())}readKeyframes(){let{unresolvedKeyframes:e,name:t,element:i,motionValue:n}=this;if(null===e[0]){let r=n?.get(),s=e[e.length-1];if(void 0!==r)e[0]=r;else if(i&&t){let n=i.readValue(t,s);null!=n&&(e[0]=n)}void 0===e[0]&&(e[0]=s),n&&void 0===r&&n.set(e[0])}for(let t=1;t<e.length;t++)e[t]??(e[t]=e[t-1])}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(e=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,e),tM.delete(this)}cancel(){"scheduled"===this.state&&(tM.delete(this),this.state="pending")}resume(){"pending"===this.state&&this.scheduleResolve()}}let tL=e=>e.startsWith("--");function t$(e){let t;return()=>(void 0===t&&(t=e()),t)}let tB=t$(()=>void 0!==window.ScrollTimeline),tU={},tz=function(e,t){let i=t$(e);return()=>tU[t]??i()}(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(e){return!1}return!0},"linearEasing"),tW=([e,t,i,n])=>`cubic-bezier(${e}, ${t}, ${i}, ${n})`,tH={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:tW([0,.65,.55,1]),circOut:tW([.55,0,1,.45]),backIn:tW([.31,.01,.66,-.59]),backOut:tW([.33,1.53,.69,.99])};function tq(e){return"function"==typeof e&&"applyToOptions"in e}class tX extends tm{constructor(e){if(super(),this.finishedTime=null,this.isStopped=!1,!e)return;let{element:t,name:i,keyframes:n,pseudoElement:r,allowFlatten:s=!1,finalKeyframe:a,onComplete:o}=e;this.isPseudoElement=!!r,this.allowFlatten=s,this.options=e,H("string"!=typeof e.type,'animateMini doesn\'t support "type" as a string. Did you mean to import { spring } from "motion"?');let l=function({type:e,...t}){return tq(e)&&tz()?e.applyToOptions(t):(t.duration??(t.duration=300),t.ease??(t.ease="easeOut"),t)}(e);this.animation=function(e,t,i,{delay:n=0,duration:r=300,repeat:s=0,repeatType:a="loop",ease:o="easeOut",times:l}={},u){let d={[t]:i};l&&(d.offset=l);let c=function e(t,i){if(t)return"function"==typeof t?tz()?ez(t,i):"ease-out":tr(t)?tW(t):Array.isArray(t)?t.map(t=>e(t,i)||tH.easeOut):tH[t]}(o,r);Array.isArray(c)&&(d.easing=c),f.value&&z.waapi++;let h={delay:n,duration:r,easing:Array.isArray(c)?"linear":c,fill:"both",iterations:s+1,direction:"reverse"===a?"alternate":"normal"};u&&(h.pseudoElement=u);let p=e.animate(d,h);return f.value&&p.finished.finally(()=>{z.waapi--}),p}(t,i,n,l,r),!1===l.autoplay&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!r){let e=tc(n,this.options,a,this.speed);this.updateMotionValue?this.updateMotionValue(e):function(e,t,i){tL(t)?e.style.setProperty(t,i):e.style[t]=i}(t,i,e),this.animation.cancel()}o?.(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),"finished"===this.state&&this.updateFinished())}pause(){this.animation.pause()}complete(){this.animation.finish?.()}cancel(){try{this.animation.cancel()}catch(e){}}stop(){if(this.isStopped)return;this.isStopped=!0;let{state:e}=this;"idle"!==e&&"finished"!==e&&(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){this.isPseudoElement||this.animation.commitStyles?.()}get duration(){return U(Number(this.animation.effect?.getComputedTiming?.().duration||0))}get time(){return U(Number(this.animation.currentTime)||0)}set time(e){this.finishedTime=null,this.animation.currentTime=B(e)}get speed(){return this.animation.playbackRate}set speed(e){e<0&&(this.finishedTime=null),this.animation.playbackRate=e}get state(){return null!==this.finishedTime?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(e){this.animation.startTime=e}attachTimeline({timeline:e,observe:t}){return(this.allowFlatten&&this.animation.effect?.updateTiming({easing:"linear"}),this.animation.onfinish=null,e&&tB())?(this.animation.timeline=e,h):t(this)}}let tK={anticipate:te,backInOut:e7,circInOut:tn};class tG extends tX{constructor(e){!function(e){"string"==typeof e.ease&&e.ease in tK&&(e.ease=tK[e.ease])}(e),tp(e),super(e),e.startTime&&(this.startTime=e.startTime),this.options=e}updateMotionValue(e){let{motionValue:t,onUpdate:i,onComplete:n,element:r,...s}=this.options;if(!t)return;if(void 0!==e)return void t.set(e);let a=new tg({...s,autoplay:!1}),o=B(this.finishedTime??this.time);t.setWithVelocity(a.sample(o-10).value,a.sample(o).value,10),a.stop()}}let tY=(e,t)=>"zIndex"!==t&&!!("number"==typeof e||Array.isArray(e)||"string"==typeof e&&(eT.test(e)||"0"===e)&&!e.startsWith("url("));function tZ(e){return"object"==typeof e&&null!==e}function tJ(e){return tZ(e)&&"offsetHeight"in e}let tQ=new Set(["opacity","clipPath","filter","transform"]),t0=t$(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));class t1 extends tm{constructor({autoplay:e=!0,delay:t=0,type:i="keyframes",repeat:n=0,repeatDelay:r=0,repeatType:s="loop",keyframes:a,name:o,motionValue:l,element:u,...d}){super(),this.stop=()=>{this._animation&&(this._animation.stop(),this.stopTimeline?.()),this.keyframeResolver?.cancel()},this.createdAt=R.now();let c={autoplay:e,delay:t,type:i,repeat:n,repeatDelay:r,repeatType:s,name:o,motionValue:l,element:u,...d},h=u?.KeyframeResolver||tF;this.keyframeResolver=new h(a,(e,t,i)=>this.onKeyframesResolved(e,t,c,!i),o,l,u),this.keyframeResolver?.scheduleResolve()}onKeyframesResolved(e,t,i,n){this.keyframeResolver=void 0;let{name:r,type:s,velocity:a,delay:o,isHandoff:l,onUpdate:u}=i;this.resolvedAt=R.now(),!function(e,t,i,n){let r=e[0];if(null===r)return!1;if("display"===t||"visibility"===t)return!0;let s=e[e.length-1],a=tY(r,t),o=tY(s,t);return W(a===o,`You are trying to animate ${t} from "${r}" to "${s}". ${r} is not an animatable value - to enable this animation set ${r} to a value animatable to ${s} via the \`style\` property.`),!!a&&!!o&&(function(e){let t=e[0];if(1===e.length)return!0;for(let i=0;i<e.length;i++)if(e[i]!==t)return!0}(e)||("spring"===i||tq(i))&&n)}(e,r,s,a)&&((p.instantAnimations||!o)&&u?.(tc(e,i,t)),e[0]=e[e.length-1],i.duration=0,i.repeat=0);let d={startTime:n?this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt:void 0,finalKeyframe:t,...i,keyframes:e},c=!l&&function(e){let{motionValue:t,name:i,repeatDelay:n,repeatType:r,damping:s,type:a}=e;if(!tJ(t?.owner?.current))return!1;let{onUpdate:o,transformTemplate:l}=t.owner.getProps();return t0()&&i&&tQ.has(i)&&("transform"!==i||!l)&&!o&&!n&&"mirror"!==r&&0!==s&&"inertia"!==a}(d)?new tG({...d,element:d.motionValue.owner.current}):new tg(d);c.finished.then(()=>this.notifyFinished()).catch(h),this.pendingTimeline&&(this.stopTimeline=c.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=c}get finished(){return this._animation?this.animation.finished:this._finished}then(e,t){return this.finished.finally(e).then(()=>{})}get animation(){return this._animation||(this.keyframeResolver?.resume(),tO=!0,tI(),tV(),tO=!1),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(e){this.animation.time=e}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(e){this.animation.speed=e}get startTime(){return this.animation.startTime}attachTimeline(e){return this._animation?this.stopTimeline=this.animation.attachTimeline(e):this.pendingTimeline=e,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){this._animation&&this.animation.cancel(),this.keyframeResolver?.cancel()}}let t2=e=>null!==e,t3={type:"spring",stiffness:500,damping:25,restSpeed:10},t5=e=>({type:"spring",stiffness:550,damping:0===e?2*Math.sqrt(550):30,restSpeed:10}),t4={type:"keyframes",duration:.8},t6={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},t8=(e,{keyframes:t})=>t.length>2?t4:j.has(e)?e.startsWith("scale")?t5(t[1]):t3:t6,t9=(e,t,i,n={},r,s)=>a=>{let o=c(n,e)||{},l=o.delay||n.delay||0,{elapsed:u=0}=n;u-=B(l);let d={keyframes:Array.isArray(i)?i:[null,i],ease:"easeOut",velocity:t.getVelocity(),...o,delay:-u,onUpdate:e=>{t.set(e),o.onUpdate&&o.onUpdate(e)},onComplete:()=>{a(),o.onComplete&&o.onComplete()},name:e,motionValue:t,element:s?void 0:r};!function({when:e,delay:t,delayChildren:i,staggerChildren:n,staggerDirection:r,repeat:s,repeatType:a,repeatDelay:o,from:l,elapsed:u,...d}){return!!Object.keys(d).length}(o)&&Object.assign(d,t8(e,d)),d.duration&&(d.duration=B(d.duration)),d.repeatDelay&&(d.repeatDelay=B(d.repeatDelay)),void 0!==d.from&&(d.keyframes[0]=d.from);let h=!1;if(!1!==d.type&&(0!==d.duration||d.repeatDelay)||(d.duration=0,0===d.delay&&(h=!0)),(p.instantAnimations||p.skipAnimations)&&(h=!0,d.duration=0,d.delay=0),d.allowFlatten=!o.type&&!o.ease,h&&!s&&void 0!==t.get()){let e=function(e,{repeat:t,repeatType:i="loop"},n){let r=e.filter(t2),s=t&&"loop"!==i&&t%2==1?0:r.length-1;return r[s]}(d.keyframes,o);if(void 0!==e)return void y.update(()=>{d.onUpdate(e),d.onComplete()})}return o.isSync?new tg(d):new t1(d)};function t7(e,t,{delay:i=0,transitionOverride:n,type:r}={}){let{transition:s=e.getDefaultTransition(),transitionEnd:a,...o}=t;n&&(s=n);let l=[],u=r&&e.animationState&&e.animationState.getState()[r];for(let t in o){let n=e.getValue(t,e.latestValues[t]??null),r=o[t];if(void 0===r||u&&function({protectedKeys:e,needsAnimating:t},i){let n=e.hasOwnProperty(i)&&!0!==t[i];return t[i]=!1,n}(u,t))continue;let a={delay:i,...c(s||{},t)},d=n.get();if(void 0!==d&&!n.isAnimating&&!Array.isArray(r)&&r===d&&!a.velocity)continue;let h=!1;if(window.MotionHandoffAnimation){let i=e.props[I];if(i){let e=window.MotionHandoffAnimation(i,t,y);null!==e&&(a.startTime=e,h=!0)}}O(e,t),n.start(t9(t,n,r,e.shouldReduceMotion&&P.has(t)?{type:!1}:a,e,h));let p=n.animation;p&&l.push(p)}return a&&Promise.all(l).then(()=>{y.update(()=>{a&&function(e,t){let{transitionEnd:i={},transition:n={},...r}=d(e,t)||{};for(let t in r={...r,...i}){var s;let i=N(s=r[t])?s[s.length-1]||0:s;e.hasValue(t)?e.getValue(t).set(i):e.addValue(t,M(i))}}(e,a)})}),l}function ie(e,t,i={}){let n=d(e,t,"exit"===i.type?e.presenceContext?.custom:void 0),{transition:r=e.getDefaultTransition()||{}}=n||{};i.transitionOverride&&(r=i.transitionOverride);let s=n?()=>Promise.all(t7(e,n,i)):()=>Promise.resolve(),a=e.variantChildren&&e.variantChildren.size?(n=0)=>{let{delayChildren:s=0,staggerChildren:a,staggerDirection:o}=r;return function(e,t,i=0,n=0,r=0,s=1,a){let o=[],l=e.variantChildren.size,u=(l-1)*r,d="function"==typeof n,c=d?e=>n(e,l):1===s?(e=0)=>e*r:(e=0)=>u-e*r;return Array.from(e.variantChildren).sort(it).forEach((e,r)=>{e.notify("AnimationStart",t),o.push(ie(e,t,{...a,delay:i+(d?0:n)+c(r)}).then(()=>e.notify("AnimationComplete",t)))}),Promise.all(o)}(e,t,n,s,a,o,i)}:()=>Promise.resolve(),{when:o}=r;if(!o)return Promise.all([s(),a(i.delay)]);{let[e,t]="beforeChildren"===o?[s,a]:[a,s];return e().then(()=>t())}}function it(e,t){return e.sortNodePosition(t)}function ii(e,t){if(!Array.isArray(t))return!1;let i=t.length;if(i!==e.length)return!1;for(let n=0;n<i;n++)if(t[n]!==e[n])return!1;return!0}function ir(e){return"string"==typeof e||Array.isArray(e)}let is=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],ia=["initial",...is],io=ia.length,il=[...is].reverse(),iu=is.length;function id(e=!1){return{isActive:e,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function ic(){return{animate:id(!0),whileInView:id(),whileHover:id(),whileTap:id(),whileDrag:id(),whileFocus:id(),exit:id()}}class ih{constructor(e){this.isMounted=!1,this.node=e}update(){}}class ip extends ih{constructor(e){super(e),e.animationState||(e.animationState=function(e){let t=t=>Promise.all(t.map(({animation:t,options:i})=>(function(e,t,i={}){let n;if(e.notify("AnimationStart",t),Array.isArray(t))n=Promise.all(t.map(t=>ie(e,t,i)));else if("string"==typeof t)n=ie(e,t,i);else{let r="function"==typeof t?d(e,t,i.custom):t;n=Promise.all(t7(e,r,i))}return n.then(()=>{e.notify("AnimationComplete",t)})})(e,t,i))),i=ic(),n=!0,r=t=>(i,n)=>{let r=d(e,n,"exit"===t?e.presenceContext?.custom:void 0);if(r){let{transition:e,transitionEnd:t,...n}=r;i={...i,...n,...t}}return i};function s(s){let{props:a}=e,l=function e(t){if(!t)return;if(!t.isControllingVariants){let i=t.parent&&e(t.parent)||{};return void 0!==t.props.initial&&(i.initial=t.props.initial),i}let i={};for(let e=0;e<io;e++){let n=ia[e],r=t.props[n];(ir(r)||!1===r)&&(i[n]=r)}return i}(e.parent)||{},u=[],c=new Set,h={},p=1/0;for(let t=0;t<iu;t++){var m,f;let d=il[t],g=i[d],y=void 0!==a[d]?a[d]:l[d],v=ir(y),x=d===s?g.isActive:null;!1===x&&(p=t);let b=y===l[d]&&y!==a[d]&&v;if(b&&n&&e.manuallyAnimateOnMount&&(b=!1),g.protectedKeys={...h},!g.isActive&&null===x||!y&&!g.prevProp||o(y)||"boolean"==typeof y)continue;let w=(m=g.prevProp,"string"==typeof(f=y)?f!==m:!!Array.isArray(f)&&!ii(f,m)),j=w||d===s&&g.isActive&&!b&&v||t>p&&v,P=!1,S=Array.isArray(y)?y:[y],E=S.reduce(r(d),{});!1===x&&(E={});let{prevResolvedValues:T={}}=g,A={...T,...E},R=t=>{j=!0,c.has(t)&&(P=!0,c.delete(t)),g.needsAnimating[t]=!0;let i=e.getValue(t);i&&(i.liveStyle=!1)};for(let e in A){let t=E[e],i=T[e];if(h.hasOwnProperty(e))continue;let n=!1;(N(t)&&N(i)?ii(t,i):t===i)?void 0!==t&&c.has(e)?R(e):g.protectedKeys[e]=!0:null!=t?R(e):c.add(e)}g.prevProp=y,g.prevResolvedValues=E,g.isActive&&(h={...h,...E}),n&&e.blockInitialAnimation&&(j=!1);let k=!(b&&w)||P;j&&k&&u.push(...S.map(e=>({animation:e,options:{type:d}})))}if(c.size){let t={};if("boolean"!=typeof a.initial){let i=d(e,Array.isArray(a.initial)?a.initial[0]:a.initial);i&&i.transition&&(t.transition=i.transition)}c.forEach(i=>{let n=e.getBaseTarget(i),r=e.getValue(i);r&&(r.liveStyle=!0),t[i]=n??null}),u.push({animation:t})}let g=!!u.length;return n&&(!1===a.initial||a.initial===a.animate)&&!e.manuallyAnimateOnMount&&(g=!1),n=!1,g?t(u):Promise.resolve()}return{animateChanges:s,setActive:function(t,n){if(i[t].isActive===n)return Promise.resolve();e.variantChildren?.forEach(e=>e.animationState?.setActive(t,n)),i[t].isActive=n;let r=s(t);for(let e in i)i[e].protectedKeys={};return r},setAnimateFunction:function(i){t=i(e)},getState:()=>i,reset:()=>{i=ic(),n=!0}}}(e))}updateAnimationControlsSubscription(){let{animate:e}=this.node.getProps();o(e)&&(this.unmountControls=e.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:e}=this.node.getProps(),{animate:t}=this.node.prevProps||{};e!==t&&this.updateAnimationControlsSubscription()}unmount(){this.node.animationState.reset(),this.unmountControls?.()}}let im=0;class ig extends ih{constructor(){super(...arguments),this.id=im++}update(){if(!this.node.presenceContext)return;let{isPresent:e,onExitComplete:t}=this.node.presenceContext,{isPresent:i}=this.node.prevPresenceContext||{};if(!this.node.animationState||e===i)return;let n=this.node.animationState.setActive("exit",!e);t&&!e&&n.then(()=>{t(this.id)})}mount(){let{register:e,onExitComplete:t}=this.node.presenceContext||{};t&&t(this.id),e&&(this.unmount=e(this.id))}unmount(){}}let iy={x:!1,y:!1};function iv(e,t,i,n={passive:!0}){return e.addEventListener(t,i,n),()=>e.removeEventListener(t,i)}let ix=e=>"mouse"===e.pointerType?"number"!=typeof e.button||e.button<=0:!1!==e.isPrimary;function ib(e){return{point:{x:e.pageX,y:e.pageY}}}let iw=e=>t=>ix(t)&&e(t,ib(t));function ij(e,t,i,n){return iv(e,t,iw(i),n)}function iP({top:e,left:t,right:i,bottom:n}){return{x:{min:t,max:i},y:{min:e,max:n}}}function iS(e){return e.max-e.min}function iE(e,t,i,n=.5){e.origin=n,e.originPoint=ek(t.min,t.max,e.origin),e.scale=iS(i)/iS(t),e.translate=ek(i.min,i.max,e.origin)-e.originPoint,(e.scale>=.9999&&e.scale<=1.0001||isNaN(e.scale))&&(e.scale=1),(e.translate>=-.01&&e.translate<=.01||isNaN(e.translate))&&(e.translate=0)}function iT(e,t,i,n){iE(e.x,t.x,i.x,n?n.originX:void 0),iE(e.y,t.y,i.y,n?n.originY:void 0)}function iA(e,t,i){e.min=i.min+t.min,e.max=e.min+iS(t)}function iR(e,t,i){e.min=t.min-i.min,e.max=e.min+iS(t)}function ik(e,t,i){iR(e.x,t.x,i.x),iR(e.y,t.y,i.y)}let iC=()=>({translate:0,scale:1,origin:0,originPoint:0}),i_=()=>({x:iC(),y:iC()}),iM=()=>({min:0,max:0}),iN=()=>({x:iM(),y:iM()});function iD(e){return[e("x"),e("y")]}function iO(e){return void 0===e||1===e}function iV({scale:e,scaleX:t,scaleY:i}){return!iO(e)||!iO(t)||!iO(i)}function iI(e){return iV(e)||iF(e)||e.z||e.rotate||e.rotateX||e.rotateY||e.skewX||e.skewY}function iF(e){var t,i;return(t=e.x)&&"0%"!==t||(i=e.y)&&"0%"!==i}function iL(e,t,i,n,r){return void 0!==r&&(e=n+r*(e-n)),n+i*(e-n)+t}function i$(e,t=0,i=1,n,r){e.min=iL(e.min,t,i,n,r),e.max=iL(e.max,t,i,n,r)}function iB(e,{x:t,y:i}){i$(e.x,t.translate,t.scale,t.originPoint),i$(e.y,i.translate,i.scale,i.originPoint)}function iU(e,t){e.min=e.min+t,e.max=e.max+t}function iz(e,t,i,n,r=.5){let s=ek(e.min,e.max,r);i$(e,t,i,s,n)}function iW(e,t){iz(e.x,t.x,t.scaleX,t.scale,t.originX),iz(e.y,t.y,t.scaleY,t.scale,t.originY)}function iH(e,t){return iP(function(e,t){if(!t)return e;let i=t({x:e.left,y:e.top}),n=t({x:e.right,y:e.bottom});return{top:i.y,left:i.x,bottom:n.y,right:n.x}}(e.getBoundingClientRect(),t))}let iq=({current:e})=>e?e.ownerDocument.defaultView:null;function iX(e){return e&&"object"==typeof e&&Object.prototype.hasOwnProperty.call(e,"current")}let iK=(e,t)=>Math.abs(e-t);class iG{constructor(e,t,{transformPagePoint:i,contextWindow:n=window,dragSnapToOrigin:r=!1,distanceThreshold:s=3}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let e=iJ(this.lastMoveEventInfo,this.history),t=null!==this.startEvent,i=function(e,t){return Math.sqrt(iK(e.x,t.x)**2+iK(e.y,t.y)**2)}(e.offset,{x:0,y:0})>=this.distanceThreshold;if(!t&&!i)return;let{point:n}=e,{timestamp:r}=x;this.history.push({...n,timestamp:r});let{onStart:s,onMove:a}=this.handlers;t||(s&&s(this.lastMoveEvent,e),this.startEvent=this.lastMoveEvent),a&&a(this.lastMoveEvent,e)},this.handlePointerMove=(e,t)=>{this.lastMoveEvent=e,this.lastMoveEventInfo=iY(t,this.transformPagePoint),y.update(this.updatePoint,!0)},this.handlePointerUp=(e,t)=>{this.end();let{onEnd:i,onSessionEnd:n,resumeAnimation:r}=this.handlers;if(this.dragSnapToOrigin&&r&&r(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let s=iJ("pointercancel"===e.type?this.lastMoveEventInfo:iY(t,this.transformPagePoint),this.history);this.startEvent&&i&&i(e,s),n&&n(e,s)},!ix(e))return;this.dragSnapToOrigin=r,this.handlers=t,this.transformPagePoint=i,this.distanceThreshold=s,this.contextWindow=n||window;let a=iY(ib(e),this.transformPagePoint),{point:o}=a,{timestamp:l}=x;this.history=[{...o,timestamp:l}];let{onSessionStart:u}=t;u&&u(e,iJ(a,this.history)),this.removeListeners=L(ij(this.contextWindow,"pointermove",this.handlePointerMove),ij(this.contextWindow,"pointerup",this.handlePointerUp),ij(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(e){this.handlers=e}end(){this.removeListeners&&this.removeListeners(),v(this.updatePoint)}}function iY(e,t){return t?{point:t(e.point)}:e}function iZ(e,t){return{x:e.x-t.x,y:e.y-t.y}}function iJ({point:e},t){return{point:e,delta:iZ(e,iQ(t)),offset:iZ(e,t[0]),velocity:function(e,t){if(e.length<2)return{x:0,y:0};let i=e.length-1,n=null,r=iQ(e);for(;i>=0&&(n=e[i],!(r.timestamp-n.timestamp>B(.1)));)i--;if(!n)return{x:0,y:0};let s=U(r.timestamp-n.timestamp);if(0===s)return{x:0,y:0};let a={x:(r.x-n.x)/s,y:(r.y-n.y)/s};return a.x===1/0&&(a.x=0),a.y===1/0&&(a.y=0),a}(t,.1)}}function iQ(e){return e[e.length-1]}function i0(e,t,i){return{min:void 0!==t?e.min+t:void 0,max:void 0!==i?e.max+i-(e.max-e.min):void 0}}function i1(e,t){let i=t.min-e.min,n=t.max-e.max;return t.max-t.min<e.max-e.min&&([i,n]=[n,i]),{min:i,max:n}}function i2(e,t,i){return{min:i3(e,t),max:i3(e,i)}}function i3(e,t){return"number"==typeof e?e:e[t]||0}let i5=new WeakMap;class i4{constructor(e){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=iN(),this.latestPointerEvent=null,this.latestPanInfo=null,this.visualElement=e}start(e,{snapToCursor:t=!1,distanceThreshold:i}={}){let{presenceContext:n}=this.visualElement;if(n&&!1===n.isPresent)return;let{dragSnapToOrigin:r}=this.getProps();this.panSession=new iG(e,{onSessionStart:e=>{let{dragSnapToOrigin:i}=this.getProps();i?this.pauseAnimation():this.stopAnimation(),t&&this.snapToCursor(ib(e).point)},onStart:(e,t)=>{let{drag:i,dragPropagation:n,onDragStart:r}=this.getProps();if(i&&!n&&(this.openDragLock&&this.openDragLock(),this.openDragLock=function(e){if("x"===e||"y"===e)if(iy[e])return null;else return iy[e]=!0,()=>{iy[e]=!1};return iy.x||iy.y?null:(iy.x=iy.y=!0,()=>{iy.x=iy.y=!1})}(i),!this.openDragLock))return;this.latestPointerEvent=e,this.latestPanInfo=t,this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),iD(e=>{let t=this.getAxisMotionValue(e).get()||0;if(ec.test(t)){let{projection:i}=this.visualElement;if(i&&i.layout){let n=i.layout.layoutBox[e];n&&(t=iS(n)*(parseFloat(t)/100))}}this.originPoint[e]=t}),r&&y.postRender(()=>r(e,t)),O(this.visualElement,"transform");let{animationState:s}=this.visualElement;s&&s.setActive("whileDrag",!0)},onMove:(e,t)=>{this.latestPointerEvent=e,this.latestPanInfo=t;let{dragPropagation:i,dragDirectionLock:n,onDirectionLock:r,onDrag:s}=this.getProps();if(!i&&!this.openDragLock)return;let{offset:a}=t;if(n&&null===this.currentDirection){this.currentDirection=function(e,t=10){let i=null;return Math.abs(e.y)>t?i="y":Math.abs(e.x)>t&&(i="x"),i}(a),null!==this.currentDirection&&r&&r(this.currentDirection);return}this.updateAxis("x",t.point,a),this.updateAxis("y",t.point,a),this.visualElement.render(),s&&s(e,t)},onSessionEnd:(e,t)=>{this.latestPointerEvent=e,this.latestPanInfo=t,this.stop(e,t),this.latestPointerEvent=null,this.latestPanInfo=null},resumeAnimation:()=>iD(e=>"paused"===this.getAnimationState(e)&&this.getAxisMotionValue(e).animation?.play())},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:r,distanceThreshold:i,contextWindow:iq(this.visualElement)})}stop(e,t){let i=e||this.latestPointerEvent,n=t||this.latestPanInfo,r=this.isDragging;if(this.cancel(),!r||!n||!i)return;let{velocity:s}=n;this.startAnimation(s);let{onDragEnd:a}=this.getProps();a&&y.postRender(()=>a(i,n))}cancel(){this.isDragging=!1;let{projection:e,animationState:t}=this.visualElement;e&&(e.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:i}=this.getProps();!i&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),t&&t.setActive("whileDrag",!1)}updateAxis(e,t,i){let{drag:n}=this.getProps();if(!i||!i6(e,n,this.currentDirection))return;let r=this.getAxisMotionValue(e),s=this.originPoint[e]+i[e];this.constraints&&this.constraints[e]&&(s=function(e,{min:t,max:i},n){return void 0!==t&&e<t?e=n?ek(t,e,n.min):Math.max(e,t):void 0!==i&&e>i&&(e=n?ek(i,e,n.max):Math.min(e,i)),e}(s,this.constraints[e],this.elastic[e])),r.set(s)}resolveConstraints(){let{dragConstraints:e,dragElastic:t}=this.getProps(),i=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):this.visualElement.projection?.layout,n=this.constraints;e&&iX(e)?this.constraints||(this.constraints=this.resolveRefConstraints()):e&&i?this.constraints=function(e,{top:t,left:i,bottom:n,right:r}){return{x:i0(e.x,i,r),y:i0(e.y,t,n)}}(i.layoutBox,e):this.constraints=!1,this.elastic=function(e=.35){return!1===e?e=0:!0===e&&(e=.35),{x:i2(e,"left","right"),y:i2(e,"top","bottom")}}(t),n!==this.constraints&&i&&this.constraints&&!this.hasMutatedConstraints&&iD(e=>{!1!==this.constraints&&this.getAxisMotionValue(e)&&(this.constraints[e]=function(e,t){let i={};return void 0!==t.min&&(i.min=t.min-e.min),void 0!==t.max&&(i.max=t.max-e.min),i}(i.layoutBox[e],this.constraints[e]))})}resolveRefConstraints(){var e;let{dragConstraints:t,onMeasureDragConstraints:i}=this.getProps();if(!t||!iX(t))return!1;let n=t.current;H(null!==n,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.");let{projection:r}=this.visualElement;if(!r||!r.layout)return!1;let s=function(e,t,i){let n=iH(e,i),{scroll:r}=t;return r&&(iU(n.x,r.offset.x),iU(n.y,r.offset.y)),n}(n,r.root,this.visualElement.getTransformPagePoint()),a=(e=r.layout.layoutBox,{x:i1(e.x,s.x),y:i1(e.y,s.y)});if(i){let e=i(function({x:e,y:t}){return{top:t.min,right:e.max,bottom:t.max,left:e.min}}(a));this.hasMutatedConstraints=!!e,e&&(a=iP(e))}return a}startAnimation(e){let{drag:t,dragMomentum:i,dragElastic:n,dragTransition:r,dragSnapToOrigin:s,onDragTransitionEnd:a}=this.getProps(),o=this.constraints||{};return Promise.all(iD(a=>{if(!i6(a,t,this.currentDirection))return;let l=o&&o[a]||{};s&&(l={min:0,max:0});let u={type:"inertia",velocity:i?e[a]:0,bounceStiffness:n?200:1e6,bounceDamping:n?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...r,...l};return this.startAxisValueAnimation(a,u)})).then(a)}startAxisValueAnimation(e,t){let i=this.getAxisMotionValue(e);return O(this.visualElement,e),i.start(t9(e,i,0,t,this.visualElement,!1))}stopAnimation(){iD(e=>this.getAxisMotionValue(e).stop())}pauseAnimation(){iD(e=>this.getAxisMotionValue(e).animation?.pause())}getAnimationState(e){return this.getAxisMotionValue(e).animation?.state}getAxisMotionValue(e){let t=`_drag${e.toUpperCase()}`,i=this.visualElement.getProps();return i[t]||this.visualElement.getValue(e,(i.initial?i.initial[e]:void 0)||0)}snapToCursor(e){iD(t=>{let{drag:i}=this.getProps();if(!i6(t,i,this.currentDirection))return;let{projection:n}=this.visualElement,r=this.getAxisMotionValue(t);if(n&&n.layout){let{min:i,max:s}=n.layout.layoutBox[t];r.set(e[t]-ek(i,s,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:e,dragConstraints:t}=this.getProps(),{projection:i}=this.visualElement;if(!iX(t)||!i||!this.constraints)return;this.stopAnimation();let n={x:0,y:0};iD(e=>{let t=this.getAxisMotionValue(e);if(t&&!1!==this.constraints){let i=t.get();n[e]=function(e,t){let i=.5,n=iS(e),r=iS(t);return r>n?i=tl(t.min,t.max-n,e.min):n>r&&(i=tl(e.min,e.max-r,t.min)),$(0,1,i)}({min:i,max:i},this.constraints[e])}});let{transformTemplate:r}=this.visualElement.getProps();this.visualElement.current.style.transform=r?r({},""):"none",i.root&&i.root.updateScroll(),i.updateLayout(),this.resolveConstraints(),iD(t=>{if(!i6(t,e,null))return;let i=this.getAxisMotionValue(t),{min:r,max:s}=this.constraints[t];i.set(ek(r,s,n[t]))})}addListeners(){if(!this.visualElement.current)return;i5.set(this.visualElement,this);let e=ij(this.visualElement.current,"pointerdown",e=>{let{drag:t,dragListener:i=!0}=this.getProps();t&&i&&this.start(e)}),t=()=>{let{dragConstraints:e}=this.getProps();iX(e)&&e.current&&(this.constraints=this.resolveRefConstraints())},{projection:i}=this.visualElement,n=i.addEventListener("measure",t);i&&!i.layout&&(i.root&&i.root.updateScroll(),i.updateLayout()),y.read(t);let r=iv(window,"resize",()=>this.scalePositionWithinConstraints()),s=i.addEventListener("didUpdate",({delta:e,hasLayoutChanged:t})=>{this.isDragging&&t&&(iD(t=>{let i=this.getAxisMotionValue(t);i&&(this.originPoint[t]+=e[t].translate,i.set(i.get()+e[t].translate))}),this.visualElement.render())});return()=>{r(),e(),n(),s&&s()}}getProps(){let e=this.visualElement.getProps(),{drag:t=!1,dragDirectionLock:i=!1,dragPropagation:n=!1,dragConstraints:r=!1,dragElastic:s=.35,dragMomentum:a=!0}=e;return{...e,drag:t,dragDirectionLock:i,dragPropagation:n,dragConstraints:r,dragElastic:s,dragMomentum:a}}}function i6(e,t,i){return(!0===t||t===e)&&(null===i||i===e)}class i8 extends ih{constructor(e){super(e),this.removeGroupControls=h,this.removeListeners=h,this.controls=new i4(e)}mount(){let{dragControls:e}=this.node.getProps();e&&(this.removeGroupControls=e.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||h}unmount(){this.removeGroupControls(),this.removeListeners()}}let i9=e=>(t,i)=>{e&&y.postRender(()=>e(t,i))};class i7 extends ih{constructor(){super(...arguments),this.removePointerDownListener=h}onPointerDown(e){this.session=new iG(e,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:iq(this.node)})}createPanHandlers(){let{onPanSessionStart:e,onPanStart:t,onPan:i,onPanEnd:n}=this.node.getProps();return{onSessionStart:i9(e),onStart:i9(t),onMove:i,onEnd:(e,t)=>{delete this.session,n&&y.postRender(()=>n(e,t))}}}mount(){this.removePointerDownListener=ij(this.node.current,"pointerdown",e=>this.onPointerDown(e))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}let{schedule:ne}=g(queueMicrotask,!1);var nt=i(3210);let ni=(0,nt.createContext)(null);function nn(e=!0){let t=(0,nt.useContext)(ni);if(null===t)return[!0,null];let{isPresent:i,onExitComplete:n,register:r}=t,s=(0,nt.useId)();(0,nt.useEffect)(()=>{if(e)return r(s)},[e]);let a=(0,nt.useCallback)(()=>e&&n&&n(s),[s,n,e]);return!i&&n?[!1,a]:[!0]}let nr=(0,nt.createContext)({}),ns=(0,nt.createContext)({}),na={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function no(e,t){return t.max===t.min?0:e/(t.max-t.min)*100}let nl={correct:(e,t)=>{if(!t.target)return e;if("string"==typeof e)if(!eh.test(e))return e;else e=parseFloat(e);let i=no(e,t.target.x),n=no(e,t.target.y);return`${i}% ${n}%`}},nu={},nd=!1;class nc extends nt.Component{componentDidMount(){let{visualElement:e,layoutGroup:t,switchLayoutGroup:i,layoutId:n}=this.props,{projection:r}=e;for(let e in np)nu[e]=np[e],X(e)&&(nu[e].isCSSVariable=!0);r&&(t.group&&t.group.add(r),i&&i.register&&n&&i.register(r),nd&&r.root.didUpdate(),r.addEventListener("animationComplete",()=>{this.safeToRemove()}),r.setOptions({...r.options,onExitComplete:()=>this.safeToRemove()})),na.hasEverUpdated=!0}getSnapshotBeforeUpdate(e){let{layoutDependency:t,visualElement:i,drag:n,isPresent:r}=this.props,{projection:s}=i;return s&&(s.isPresent=r,nd=!0,n||e.layoutDependency!==t||void 0===t||e.isPresent!==r?s.willUpdate():this.safeToRemove(),e.isPresent!==r&&(r?s.promote():s.relegate()||y.postRender(()=>{let e=s.getStack();e&&e.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:e}=this.props.visualElement;e&&(e.root.didUpdate(),ne.postRender(()=>{!e.currentAnimation&&e.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:e,layoutGroup:t,switchLayoutGroup:i}=this.props,{projection:n}=e;n&&(n.scheduleCheckAfterUnmount(),t&&t.group&&t.group.remove(n),i&&i.deregister&&i.deregister(n))}safeToRemove(){let{safeToRemove:e}=this.props;e&&e()}render(){return null}}function nh(e){let[t,i]=nn(),n=(0,nt.useContext)(nr);return(0,a.jsx)(nc,{...e,layoutGroup:n,switchLayoutGroup:(0,nt.useContext)(ns),isPresent:t,safeToRemove:i})}let np={borderRadius:{...nl,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:nl,borderTopRightRadius:nl,borderBottomLeftRadius:nl,borderBottomRightRadius:nl,boxShadow:{correct:(e,{treeScale:t,projectionDelta:i})=>{let n=eT.parse(e);if(n.length>5)return e;let r=eT.createTransformer(e),s=+("number"!=typeof n[0]),a=i.x.scale*t.x,o=i.y.scale*t.y;n[0+s]/=a,n[1+s]/=o;let l=ek(a,o,.5);return"number"==typeof n[2+s]&&(n[2+s]/=l),"number"==typeof n[3+s]&&(n[3+s]/=l),r(n)}}};function nm(e){return tZ(e)&&"ownerSVGElement"in e}let nf=(e,t)=>e.depth-t.depth;class ng{constructor(){this.children=[],this.isDirty=!1}add(e){S(this.children,e),this.isDirty=!0}remove(e){E(this.children,e),this.isDirty=!0}forEach(e){this.isDirty&&this.children.sort(nf),this.isDirty=!1,this.children.forEach(e)}}function ny(e){return D(e)?e.get():e}let nv=["TopLeft","TopRight","BottomLeft","BottomRight"],nx=nv.length,nb=e=>"string"==typeof e?parseFloat(e):e,nw=e=>"number"==typeof e||eh.test(e);function nj(e,t){return void 0!==e[t]?e[t]:e.borderRadius}let nP=nE(0,.5,ti),nS=nE(.5,.95,h);function nE(e,t,i){return n=>n<e?0:n>t?1:i(tl(e,t,n))}function nT(e,t){e.min=t.min,e.max=t.max}function nA(e,t){nT(e.x,t.x),nT(e.y,t.y)}function nR(e,t){e.translate=t.translate,e.scale=t.scale,e.originPoint=t.originPoint,e.origin=t.origin}function nk(e,t,i,n,r){return e-=t,e=n+1/i*(e-n),void 0!==r&&(e=n+1/r*(e-n)),e}function nC(e,t,[i,n,r],s,a){!function(e,t=0,i=1,n=.5,r,s=e,a=e){if(ec.test(t)&&(t=parseFloat(t),t=ek(a.min,a.max,t/100)-a.min),"number"!=typeof t)return;let o=ek(s.min,s.max,n);e===s&&(o-=t),e.min=nk(e.min,t,i,o,r),e.max=nk(e.max,t,i,o,r)}(e,t[i],t[n],t[r],t.scale,s,a)}let n_=["x","scaleX","originX"],nM=["y","scaleY","originY"];function nN(e,t,i,n){nC(e.x,t,n_,i?i.x:void 0,n?n.x:void 0),nC(e.y,t,nM,i?i.y:void 0,n?n.y:void 0)}function nD(e){return 0===e.translate&&1===e.scale}function nO(e){return nD(e.x)&&nD(e.y)}function nV(e,t){return e.min===t.min&&e.max===t.max}function nI(e,t){return Math.round(e.min)===Math.round(t.min)&&Math.round(e.max)===Math.round(t.max)}function nF(e,t){return nI(e.x,t.x)&&nI(e.y,t.y)}function nL(e){return iS(e.x)/iS(e.y)}function n$(e,t){return e.translate===t.translate&&e.scale===t.scale&&e.originPoint===t.originPoint}class nB{constructor(){this.members=[]}add(e){S(this.members,e),e.scheduleRender()}remove(e){if(E(this.members,e),e===this.prevLead&&(this.prevLead=void 0),e===this.lead){let e=this.members[this.members.length-1];e&&this.promote(e)}}relegate(e){let t,i=this.members.findIndex(t=>e===t);if(0===i)return!1;for(let e=i;e>=0;e--){let i=this.members[e];if(!1!==i.isPresent){t=i;break}}return!!t&&(this.promote(t),!0)}promote(e,t){let i=this.lead;if(e!==i&&(this.prevLead=i,this.lead=e,e.show(),i)){i.instance&&i.scheduleRender(),e.scheduleRender(),e.resumeFrom=i,t&&(e.resumeFrom.preserveOpacity=!0),i.snapshot&&(e.snapshot=i.snapshot,e.snapshot.latestValues=i.animationValues||i.latestValues),e.root&&e.root.isUpdating&&(e.isLayoutDirty=!0);let{crossfade:n}=e.options;!1===n&&i.hide()}}exitAnimationComplete(){this.members.forEach(e=>{let{options:t,resumingFrom:i}=e;t.onExitComplete&&t.onExitComplete(),i&&i.options.onExitComplete&&i.options.onExitComplete()})}scheduleRender(){this.members.forEach(e=>{e.instance&&e.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}let nU={nodes:0,calculatedTargetDeltas:0,calculatedProjections:0},nz=["","X","Y","Z"],nW=0;function nH(e,t,i,n){let{latestValues:r}=t;r[e]&&(i[e]=r[e],t.setStaticValue(e,0),n&&(n[e]=0))}function nq({attachResizeListener:e,defaultParent:t,measureScroll:i,checkIsScrollRoot:n,resetTransform:r}){return class{constructor(e={},i=t?.()){this.id=nW++,this.animationId=0,this.animationCommitId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,f.value&&(nU.nodes=nU.calculatedTargetDeltas=nU.calculatedProjections=0),this.nodes.forEach(nG),this.nodes.forEach(n2),this.nodes.forEach(n3),this.nodes.forEach(nY),f.addProjectionMetrics&&f.addProjectionMetrics(nU)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=e,this.root=i?i.root||i:this,this.path=i?[...i.path,i]:[],this.parent=i,this.depth=i?i.depth+1:0;for(let e=0;e<this.path.length;e++)this.path[e].shouldResetTransform=!0;this.root===this&&(this.nodes=new ng)}addEventListener(e,t){return this.eventHandlers.has(e)||this.eventHandlers.set(e,new T),this.eventHandlers.get(e).add(t)}notifyListeners(e,...t){let i=this.eventHandlers.get(e);i&&i.notify(...t)}hasListeners(e){return this.eventHandlers.has(e)}mount(t){if(this.instance)return;this.isSVG=nm(t)&&!(nm(t)&&"svg"===t.tagName),this.instance=t;let{layoutId:i,layout:n,visualElement:r}=this.options;if(r&&!r.current&&r.mount(t),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(n||i)&&(this.isLayoutDirty=!0),e){let i,n=0,r=()=>this.root.updateBlockedByResize=!1;y.read(()=>{n=window.innerWidth}),e(t,()=>{let e=window.innerWidth;e!==n&&(n=e,this.root.updateBlockedByResize=!0,i&&i(),i=function(e,t){let i=R.now(),n=({timestamp:r})=>{let s=r-i;s>=250&&(v(n),e(s-t))};return y.setup(n,!0),()=>v(n)}(r,250),na.hasAnimatedSinceResize&&(na.hasAnimatedSinceResize=!1,this.nodes.forEach(n1)))})}i&&this.root.registerSharedNode(i,this),!1!==this.options.animate&&r&&(i||n)&&this.addEventListener("didUpdate",({delta:e,hasLayoutChanged:t,hasRelativeLayoutChanged:i,layout:n})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let s=this.options.transition||r.getDefaultTransition()||n7,{onLayoutAnimationStart:a,onLayoutAnimationComplete:o}=r.getProps(),l=!this.targetLayout||!nF(this.targetLayout,n),u=!t&&i;if(this.options.layoutRoot||this.resumeFrom||u||t&&(l||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0);let t={...c(s,"layout"),onPlay:a,onComplete:o};(r.shouldReduceMotion||this.options.layoutRoot)&&(t.delay=0,t.type=!1),this.startAnimation(t),this.setAnimationOrigin(e,u)}else t||n1(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=n})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let e=this.getStack();e&&e.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),v(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(n5),this.animationId++)}getTransformTemplate(){let{visualElement:e}=this.options;return e&&e.getProps().transformTemplate}willUpdate(e=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&function e(t){if(t.hasCheckedOptimisedAppear=!0,t.root===t)return;let{visualElement:i}=t.options;if(!i)return;let n=i.props[I];if(window.MotionHasOptimisedAnimation(n,"transform")){let{layout:e,layoutId:i}=t.options;window.MotionCancelOptimisedAnimation(n,"transform",y,!(e||i))}let{parent:r}=t;r&&!r.hasCheckedOptimisedAppear&&e(r)}(this),this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let e=0;e<this.path.length;e++){let t=this.path[e];t.shouldResetTransform=!0,t.updateScroll("snapshot"),t.options.layoutRoot&&t.willUpdate(!1)}let{layoutId:t,layout:i}=this.options;if(void 0===t&&!i)return;let n=this.getTransformTemplate();this.prevTransformTemplateValue=n?n(this.latestValues,""):void 0,this.updateSnapshot(),e&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(nJ);return}if(this.animationId<=this.animationCommitId)return void this.nodes.forEach(nQ);this.animationCommitId=this.animationId,this.isUpdating?(this.isUpdating=!1,this.nodes.forEach(n0),this.nodes.forEach(nX),this.nodes.forEach(nK)):this.nodes.forEach(nQ),this.clearAllSnapshots();let e=R.now();x.delta=$(0,1e3/60,e-x.timestamp),x.timestamp=e,x.isProcessing=!0,b.update.process(x),b.preRender.process(x),b.render.process(x),x.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,ne.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(nZ),this.sharedNodes.forEach(n4)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,y.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){y.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure(),!this.snapshot||iS(this.snapshot.measuredBox.x)||iS(this.snapshot.measuredBox.y)||(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let e=0;e<this.path.length;e++)this.path[e].updateScroll();let e=this.layout;this.layout=this.measure(!1),this.layoutCorrected=iN(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:t}=this.options;t&&t.notify("LayoutMeasure",this.layout.layoutBox,e?e.layoutBox:void 0)}updateScroll(e="measure"){let t=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===e&&(t=!1),t&&this.instance){let t=n(this.instance);this.scroll={animationId:this.root.animationId,phase:e,isRoot:t,offset:i(this.instance),wasRoot:this.scroll?this.scroll.isRoot:t}}}resetTransform(){if(!r)return;let e=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,t=this.projectionDelta&&!nO(this.projectionDelta),i=this.getTransformTemplate(),n=i?i(this.latestValues,""):void 0,s=n!==this.prevTransformTemplateValue;e&&this.instance&&(t||iI(this.latestValues)||s)&&(r(this.instance,n),this.shouldResetTransform=!1,this.scheduleRender())}measure(e=!0){var t;let i=this.measurePageBox(),n=this.removeElementScroll(i);return e&&(n=this.removeTransform(n)),ri((t=n).x),ri(t.y),{animationId:this.root.animationId,measuredBox:i,layoutBox:n,latestValues:{},source:this.id}}measurePageBox(){let{visualElement:e}=this.options;if(!e)return iN();let t=e.measureViewportBox();if(!(this.scroll?.wasRoot||this.path.some(rr))){let{scroll:e}=this.root;e&&(iU(t.x,e.offset.x),iU(t.y,e.offset.y))}return t}removeElementScroll(e){let t=iN();if(nA(t,e),this.scroll?.wasRoot)return t;for(let i=0;i<this.path.length;i++){let n=this.path[i],{scroll:r,options:s}=n;n!==this.root&&r&&s.layoutScroll&&(r.wasRoot&&nA(t,e),iU(t.x,r.offset.x),iU(t.y,r.offset.y))}return t}applyTransform(e,t=!1){let i=iN();nA(i,e);for(let e=0;e<this.path.length;e++){let n=this.path[e];!t&&n.options.layoutScroll&&n.scroll&&n!==n.root&&iW(i,{x:-n.scroll.offset.x,y:-n.scroll.offset.y}),iI(n.latestValues)&&iW(i,n.latestValues)}return iI(this.latestValues)&&iW(i,this.latestValues),i}removeTransform(e){let t=iN();nA(t,e);for(let e=0;e<this.path.length;e++){let i=this.path[e];if(!i.instance||!iI(i.latestValues))continue;iV(i.latestValues)&&i.updateSnapshot();let n=iN();nA(n,i.measurePageBox()),nN(t,i.latestValues,i.snapshot?i.snapshot.layoutBox:void 0,n)}return iI(this.latestValues)&&nN(t,this.latestValues),t}setTargetDelta(e){this.targetDelta=e,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(e){this.options={...this.options,...e,crossfade:void 0===e.crossfade||e.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==x.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(e=!1){let t=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=t.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=t.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=t.isSharedProjectionDirty);let i=!!this.resumingFrom||this!==t;if(!(e||i&&this.isSharedProjectionDirty||this.isProjectionDirty||this.parent?.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;let{layout:n,layoutId:r}=this.options;if(this.layout&&(n||r)){if(this.resolvedRelativeTargetAt=x.timestamp,!this.targetDelta&&!this.relativeTarget){let e=this.getClosestProjectingParent();e&&e.layout&&1!==this.animationProgress?(this.relativeParent=e,this.forceRelativeParentToResolveTarget(),this.relativeTarget=iN(),this.relativeTargetOrigin=iN(),ik(this.relativeTargetOrigin,this.layout.layoutBox,e.layout.layoutBox),nA(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if(this.target||(this.target=iN(),this.targetWithTransforms=iN()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target){var s,a,o;this.forceRelativeParentToResolveTarget(),s=this.target,a=this.relativeTarget,o=this.relativeParent.target,iA(s.x,a.x,o.x),iA(s.y,a.y,o.y)}else this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):nA(this.target,this.layout.layoutBox),iB(this.target,this.targetDelta)):nA(this.target,this.layout.layoutBox);if(this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let e=this.getClosestProjectingParent();e&&!!e.resumingFrom==!!this.resumingFrom&&!e.options.layoutScroll&&e.target&&1!==this.animationProgress?(this.relativeParent=e,this.forceRelativeParentToResolveTarget(),this.relativeTarget=iN(),this.relativeTargetOrigin=iN(),ik(this.relativeTargetOrigin,this.target,e.target),nA(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}f.value&&nU.calculatedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||iV(this.parent.latestValues)||iF(this.parent.latestValues)))if(this.parent.isProjecting())return this.parent;else return this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){let e=this.getLead(),t=!!this.resumingFrom||this!==e,i=!0;if((this.isProjectionDirty||this.parent?.isProjectionDirty)&&(i=!1),t&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(i=!1),this.resolvedRelativeTargetAt===x.timestamp&&(i=!1),i)return;let{layout:n,layoutId:r}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(n||r))return;nA(this.layoutCorrected,this.layout.layoutBox);let s=this.treeScale.x,a=this.treeScale.y;!function(e,t,i,n=!1){let r,s,a=i.length;if(a){t.x=t.y=1;for(let o=0;o<a;o++){s=(r=i[o]).projectionDelta;let{visualElement:a}=r.options;(!a||!a.props.style||"contents"!==a.props.style.display)&&(n&&r.options.layoutScroll&&r.scroll&&r!==r.root&&iW(e,{x:-r.scroll.offset.x,y:-r.scroll.offset.y}),s&&(t.x*=s.x.scale,t.y*=s.y.scale,iB(e,s)),n&&iI(r.latestValues)&&iW(e,r.latestValues))}t.x<1.0000000000001&&t.x>.999999999999&&(t.x=1),t.y<1.0000000000001&&t.y>.999999999999&&(t.y=1)}}(this.layoutCorrected,this.treeScale,this.path,t),e.layout&&!e.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(e.target=e.layout.layoutBox,e.targetWithTransforms=iN());let{target:o}=e;if(!o){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}this.projectionDelta&&this.prevProjectionDelta?(nR(this.prevProjectionDelta.x,this.projectionDelta.x),nR(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),iT(this.projectionDelta,this.layoutCorrected,o,this.latestValues),this.treeScale.x===s&&this.treeScale.y===a&&n$(this.projectionDelta.x,this.prevProjectionDelta.x)&&n$(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",o)),f.value&&nU.calculatedProjections++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(e=!0){if(this.options.visualElement?.scheduleRender(),e){let e=this.getStack();e&&e.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=i_(),this.projectionDelta=i_(),this.projectionDeltaWithTransform=i_()}setAnimationOrigin(e,t=!1){let i,n=this.snapshot,r=n?n.latestValues:{},s={...this.latestValues},a=i_();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!t;let o=iN(),l=(n?n.source:void 0)!==(this.layout?this.layout.source:void 0),u=this.getStack(),d=!u||u.members.length<=1,c=!!(l&&!d&&!0===this.options.crossfade&&!this.path.some(n9));this.animationProgress=0,this.mixTargetDelta=t=>{let n=t/1e3;if(n6(a.x,e.x,n),n6(a.y,e.y,n),this.setTargetDelta(a),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var u,h,p,m,f,g;ik(o,this.layout.layoutBox,this.relativeParent.layout.layoutBox),p=this.relativeTarget,m=this.relativeTargetOrigin,f=o,g=n,n8(p.x,m.x,f.x,g),n8(p.y,m.y,f.y,g),i&&(u=this.relativeTarget,h=i,nV(u.x,h.x)&&nV(u.y,h.y))&&(this.isProjectionDirty=!1),i||(i=iN()),nA(i,this.relativeTarget)}l&&(this.animationValues=s,function(e,t,i,n,r,s){r?(e.opacity=ek(0,i.opacity??1,nP(n)),e.opacityExit=ek(t.opacity??1,0,nS(n))):s&&(e.opacity=ek(t.opacity??1,i.opacity??1,n));for(let r=0;r<nx;r++){let s=`border${nv[r]}Radius`,a=nj(t,s),o=nj(i,s);(void 0!==a||void 0!==o)&&(a||(a=0),o||(o=0),0===a||0===o||nw(a)===nw(o)?(e[s]=Math.max(ek(nb(a),nb(o),n),0),(ec.test(o)||ec.test(a))&&(e[s]+="%")):e[s]=o)}(t.rotate||i.rotate)&&(e.rotate=ek(t.rotate||0,i.rotate||0,n))}(s,r,this.latestValues,n,c,d)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=n},this.mixTargetDelta(1e3*!!this.options.layoutRoot)}startAnimation(e){this.notifyListeners("animationStart"),this.currentAnimation?.stop(),this.resumingFrom?.currentAnimation?.stop(),this.pendingAnimation&&(v(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=y.update(()=>{na.hasAnimatedSinceResize=!0,z.layout++,this.motionValue||(this.motionValue=M(0)),this.currentAnimation=function(e,t,i){let n=D(e)?e:M(e);return n.start(t9("",n,t,i)),n.animation}(this.motionValue,[0,1e3],{...e,velocity:0,isSync:!0,onUpdate:t=>{this.mixTargetDelta(t),e.onUpdate&&e.onUpdate(t)},onStop:()=>{z.layout--},onComplete:()=>{z.layout--,e.onComplete&&e.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let e=this.getStack();e&&e.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){let e=this.getLead(),{targetWithTransforms:t,target:i,layout:n,latestValues:r}=e;if(t&&i&&n){if(this!==e&&this.layout&&n&&rn(this.options.animationType,this.layout.layoutBox,n.layoutBox)){i=this.target||iN();let t=iS(this.layout.layoutBox.x);i.x.min=e.target.x.min,i.x.max=i.x.min+t;let n=iS(this.layout.layoutBox.y);i.y.min=e.target.y.min,i.y.max=i.y.min+n}nA(t,i),iW(t,r),iT(this.projectionDeltaWithTransform,this.layoutCorrected,t,r)}}registerSharedNode(e,t){this.sharedNodes.has(e)||this.sharedNodes.set(e,new nB),this.sharedNodes.get(e).add(t);let i=t.options.initialPromotionConfig;t.promote({transition:i?i.transition:void 0,preserveFollowOpacity:i&&i.shouldPreserveFollowOpacity?i.shouldPreserveFollowOpacity(t):void 0})}isLead(){let e=this.getStack();return!e||e.lead===this}getLead(){let{layoutId:e}=this.options;return e&&this.getStack()?.lead||this}getPrevLead(){let{layoutId:e}=this.options;return e?this.getStack()?.prevLead:void 0}getStack(){let{layoutId:e}=this.options;if(e)return this.root.sharedNodes.get(e)}promote({needsReset:e,transition:t,preserveFollowOpacity:i}={}){let n=this.getStack();n&&n.promote(this,i),e&&(this.projectionDelta=void 0,this.needsReset=!0),t&&this.setOptions({transition:t})}relegate(){let e=this.getStack();return!!e&&e.relegate(this)}resetSkewAndRotation(){let{visualElement:e}=this.options;if(!e)return;let t=!1,{latestValues:i}=e;if((i.z||i.rotate||i.rotateX||i.rotateY||i.rotateZ||i.skewX||i.skewY)&&(t=!0),!t)return;let n={};i.z&&nH("z",e,n,this.animationValues);for(let t=0;t<nz.length;t++)nH(`rotate${nz[t]}`,e,n,this.animationValues),nH(`skew${nz[t]}`,e,n,this.animationValues);for(let t in e.render(),n)e.setStaticValue(t,n[t]),this.animationValues&&(this.animationValues[t]=n[t]);e.scheduleRender()}applyProjectionStyles(e,t){if(!this.instance||this.isSVG)return;if(!this.isVisible){e.visibility="hidden";return}let i=this.getTransformTemplate();if(this.needsReset){this.needsReset=!1,e.visibility="",e.opacity="",e.pointerEvents=ny(t?.pointerEvents)||"",e.transform=i?i(this.latestValues,""):"none";return}let n=this.getLead();if(!this.projectionDelta||!this.layout||!n.target){this.options.layoutId&&(e.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,e.pointerEvents=ny(t?.pointerEvents)||""),this.hasProjected&&!iI(this.latestValues)&&(e.transform=i?i({},""):"none",this.hasProjected=!1);return}e.visibility="";let r=n.animationValues||n.latestValues;this.applyTransformsToTarget();let s=function(e,t,i){let n="",r=e.x.translate/t.x,s=e.y.translate/t.y,a=i?.z||0;if((r||s||a)&&(n=`translate3d(${r}px, ${s}px, ${a}px) `),(1!==t.x||1!==t.y)&&(n+=`scale(${1/t.x}, ${1/t.y}) `),i){let{transformPerspective:e,rotate:t,rotateX:r,rotateY:s,skewX:a,skewY:o}=i;e&&(n=`perspective(${e}px) ${n}`),t&&(n+=`rotate(${t}deg) `),r&&(n+=`rotateX(${r}deg) `),s&&(n+=`rotateY(${s}deg) `),a&&(n+=`skewX(${a}deg) `),o&&(n+=`skewY(${o}deg) `)}let o=e.x.scale*t.x,l=e.y.scale*t.y;return(1!==o||1!==l)&&(n+=`scale(${o}, ${l})`),n||"none"}(this.projectionDeltaWithTransform,this.treeScale,r);i&&(s=i(r,s)),e.transform=s;let{x:a,y:o}=this.projectionDelta;for(let t in e.transformOrigin=`${100*a.origin}% ${100*o.origin}% 0`,n.animationValues?e.opacity=n===this?r.opacity??this.latestValues.opacity??1:this.preserveOpacity?this.latestValues.opacity:r.opacityExit:e.opacity=n===this?void 0!==r.opacity?r.opacity:"":void 0!==r.opacityExit?r.opacityExit:0,nu){if(void 0===r[t])continue;let{correct:i,applyTo:a,isCSSVariable:o}=nu[t],l="none"===s?r[t]:i(r[t],n);if(a){let t=a.length;for(let i=0;i<t;i++)e[a[i]]=l}else o?this.options.visualElement.renderState.vars[t]=l:e[t]=l}this.options.layoutId&&(e.pointerEvents=n===this?ny(t?.pointerEvents)||"":"none")}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(e=>e.currentAnimation?.stop()),this.root.nodes.forEach(nJ),this.root.sharedNodes.clear()}}}function nX(e){e.updateLayout()}function nK(e){let t=e.resumeFrom?.snapshot||e.snapshot;if(e.isLead()&&e.layout&&t&&e.hasListeners("didUpdate")){let{layoutBox:i,measuredBox:n}=e.layout,{animationType:r}=e.options,s=t.source!==e.layout.source;"size"===r?iD(e=>{let n=s?t.measuredBox[e]:t.layoutBox[e],r=iS(n);n.min=i[e].min,n.max=n.min+r}):rn(r,t.layoutBox,i)&&iD(n=>{let r=s?t.measuredBox[n]:t.layoutBox[n],a=iS(i[n]);r.max=r.min+a,e.relativeTarget&&!e.currentAnimation&&(e.isProjectionDirty=!0,e.relativeTarget[n].max=e.relativeTarget[n].min+a)});let a=i_();iT(a,i,t.layoutBox);let o=i_();s?iT(o,e.applyTransform(n,!0),t.measuredBox):iT(o,i,t.layoutBox);let l=!nO(a),u=!1;if(!e.resumeFrom){let n=e.getClosestProjectingParent();if(n&&!n.resumeFrom){let{snapshot:r,layout:s}=n;if(r&&s){let a=iN();ik(a,t.layoutBox,r.layoutBox);let o=iN();ik(o,i,s.layoutBox),nF(a,o)||(u=!0),n.options.layoutRoot&&(e.relativeTarget=o,e.relativeTargetOrigin=a,e.relativeParent=n)}}}e.notifyListeners("didUpdate",{layout:i,snapshot:t,delta:o,layoutDelta:a,hasLayoutChanged:l,hasRelativeLayoutChanged:u})}else if(e.isLead()){let{onExitComplete:t}=e.options;t&&t()}e.options.transition=void 0}function nG(e){f.value&&nU.nodes++,e.parent&&(e.isProjecting()||(e.isProjectionDirty=e.parent.isProjectionDirty),e.isSharedProjectionDirty||(e.isSharedProjectionDirty=!!(e.isProjectionDirty||e.parent.isProjectionDirty||e.parent.isSharedProjectionDirty)),e.isTransformDirty||(e.isTransformDirty=e.parent.isTransformDirty))}function nY(e){e.isProjectionDirty=e.isSharedProjectionDirty=e.isTransformDirty=!1}function nZ(e){e.clearSnapshot()}function nJ(e){e.clearMeasurements()}function nQ(e){e.isLayoutDirty=!1}function n0(e){let{visualElement:t}=e.options;t&&t.getProps().onBeforeLayoutMeasure&&t.notify("BeforeLayoutMeasure"),e.resetTransform()}function n1(e){e.finishAnimation(),e.targetDelta=e.relativeTarget=e.target=void 0,e.isProjectionDirty=!0}function n2(e){e.resolveTargetDelta()}function n3(e){e.calcProjection()}function n5(e){e.resetSkewAndRotation()}function n4(e){e.removeLeadSnapshot()}function n6(e,t,i){e.translate=ek(t.translate,0,i),e.scale=ek(t.scale,1,i),e.origin=t.origin,e.originPoint=t.originPoint}function n8(e,t,i,n){e.min=ek(t.min,i.min,n),e.max=ek(t.max,i.max,n)}function n9(e){return e.animationValues&&void 0!==e.animationValues.opacityExit}let n7={duration:.45,ease:[.4,0,.1,1]},re=e=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(e),rt=re("applewebkit/")&&!re("chrome/")?Math.round:h;function ri(e){e.min=rt(e.min),e.max=rt(e.max)}function rn(e,t,i){return"position"===e||"preserve-aspect"===e&&!(.2>=Math.abs(nL(t)-nL(i)))}function rr(e){return e!==e.root&&e.scroll?.wasRoot}let rs=nq({attachResizeListener:(e,t)=>iv(e,"resize",t),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),ra={current:void 0},ro=nq({measureScroll:e=>({x:e.scrollLeft,y:e.scrollTop}),defaultParent:()=>{if(!ra.current){let e=new rs({});e.mount(window),e.setOptions({layoutScroll:!0}),ra.current=e}return ra.current},resetTransform:(e,t)=>{e.style.transform=void 0!==t?t:"none"},checkIsScrollRoot:e=>"fixed"===window.getComputedStyle(e).position});function rl(e,t){let i=function(e,t,i){if(e instanceof EventTarget)return[e];if("string"==typeof e){let t=document,i=(void 0)??t.querySelectorAll(e);return i?Array.from(i):[]}return Array.from(e)}(e),n=new AbortController;return[i,{passive:!0,...t,signal:n.signal},()=>n.abort()]}function ru(e){return!("touch"===e.pointerType||iy.x||iy.y)}function rd(e,t,i){let{props:n}=e;e.animationState&&n.whileHover&&e.animationState.setActive("whileHover","Start"===i);let r=n["onHover"+i];r&&y.postRender(()=>r(t,ib(t)))}class rc extends ih{mount(){let{current:e}=this.node;e&&(this.unmount=function(e,t,i={}){let[n,r,s]=rl(e,i),a=e=>{if(!ru(e))return;let{target:i}=e,n=t(i,e);if("function"!=typeof n||!i)return;let s=e=>{ru(e)&&(n(e),i.removeEventListener("pointerleave",s))};i.addEventListener("pointerleave",s,r)};return n.forEach(e=>{e.addEventListener("pointerenter",a,r)}),s}(e,(e,t)=>(rd(this.node,t,"Start"),e=>rd(this.node,e,"End"))))}unmount(){}}class rh extends ih{constructor(){super(...arguments),this.isActive=!1}onFocus(){let e=!1;try{e=this.node.current.matches(":focus-visible")}catch(t){e=!0}e&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=L(iv(this.node.current,"focus",()=>this.onFocus()),iv(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}let rp=(e,t)=>!!t&&(e===t||rp(e,t.parentElement)),rm=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]),rf=new WeakSet;function rg(e){return t=>{"Enter"===t.key&&e(t)}}function ry(e,t){e.dispatchEvent(new PointerEvent("pointer"+t,{isPrimary:!0,bubbles:!0}))}let rv=(e,t)=>{let i=e.currentTarget;if(!i)return;let n=rg(()=>{if(rf.has(i))return;ry(i,"down");let e=rg(()=>{ry(i,"up")});i.addEventListener("keyup",e,t),i.addEventListener("blur",()=>ry(i,"cancel"),t)});i.addEventListener("keydown",n,t),i.addEventListener("blur",()=>i.removeEventListener("keydown",n),t)};function rx(e){return ix(e)&&!(iy.x||iy.y)}function rb(e,t,i){let{props:n}=e;if(e.current instanceof HTMLButtonElement&&e.current.disabled)return;e.animationState&&n.whileTap&&e.animationState.setActive("whileTap","Start"===i);let r=n["onTap"+("End"===i?"":i)];r&&y.postRender(()=>r(t,ib(t)))}class rw extends ih{mount(){let{current:e}=this.node;e&&(this.unmount=function(e,t,i={}){let[n,r,s]=rl(e,i),a=e=>{let n=e.currentTarget;if(!rx(e))return;rf.add(n);let s=t(n,e),a=(e,t)=>{window.removeEventListener("pointerup",o),window.removeEventListener("pointercancel",l),rf.has(n)&&rf.delete(n),rx(e)&&"function"==typeof s&&s(e,{success:t})},o=e=>{a(e,n===window||n===document||i.useGlobalTarget||rp(n,e.target))},l=e=>{a(e,!1)};window.addEventListener("pointerup",o,r),window.addEventListener("pointercancel",l,r)};return n.forEach(e=>{((i.useGlobalTarget?window:e).addEventListener("pointerdown",a,r),tJ(e))&&(e.addEventListener("focus",e=>rv(e,r)),rm.has(e.tagName)||-1!==e.tabIndex||e.hasAttribute("tabindex")||(e.tabIndex=0))}),s}(e,(e,t)=>(rb(this.node,t,"Start"),(e,{success:t})=>rb(this.node,e,t?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}let rj=new WeakMap,rP=new WeakMap,rS=e=>{let t=rj.get(e.target);t&&t(e)},rE=e=>{e.forEach(rS)},rT={some:0,all:1};class rA extends ih{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();let{viewport:e={}}=this.node.getProps(),{root:t,margin:i,amount:n="some",once:r}=e,s={root:t?t.current:void 0,rootMargin:i,threshold:"number"==typeof n?n:rT[n]};return function(e,t,i){let n=function({root:e,...t}){let i=e||document;rP.has(i)||rP.set(i,{});let n=rP.get(i),r=JSON.stringify(t);return n[r]||(n[r]=new IntersectionObserver(rE,{root:e,...t})),n[r]}(t);return rj.set(e,i),n.observe(e),()=>{rj.delete(e),n.unobserve(e)}}(this.node.current,s,e=>{let{isIntersecting:t}=e;if(this.isInView===t||(this.isInView=t,r&&!t&&this.hasEnteredView))return;t&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",t);let{onViewportEnter:i,onViewportLeave:n}=this.node.getProps(),s=t?i:n;s&&s(e)})}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:e,prevProps:t}=this.node;["amount","margin","root"].some(function({viewport:e={}},{viewport:t={}}={}){return i=>e[i]!==t[i]}(e,t))&&this.startObserver()}unmount(){}}let rR=(0,nt.createContext)({strict:!1}),rk=(0,nt.createContext)({transformPagePoint:e=>e,isStatic:!1,reducedMotion:"never"}),rC=(0,nt.createContext)({});function r_(e){return o(e.animate)||ia.some(t=>ir(e[t]))}function rM(e){return!!(r_(e)||e.variants)}function rN(e){return Array.isArray(e)?e.join(" "):e}let rD="undefined"!=typeof window,rO={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},rV={};for(let e in rO)rV[e]={isEnabled:t=>rO[e].some(e=>!!t[e])};let rI=Symbol.for("motionComponentSymbol"),rF=rD?nt.useLayoutEffect:nt.useEffect;function rL(e,{layout:t,layoutId:i}){return j.has(e)||e.startsWith("origin")||(t||void 0!==i)&&(!!nu[e]||"opacity"===e)}let r$=(e,t)=>t&&"number"==typeof e?t.transform(e):e,rB={...Z,transform:Math.round},rU={borderWidth:eh,borderTopWidth:eh,borderRightWidth:eh,borderBottomWidth:eh,borderLeftWidth:eh,borderRadius:eh,radius:eh,borderTopLeftRadius:eh,borderTopRightRadius:eh,borderBottomRightRadius:eh,borderBottomLeftRadius:eh,width:eh,maxWidth:eh,height:eh,maxHeight:eh,top:eh,right:eh,bottom:eh,left:eh,padding:eh,paddingTop:eh,paddingRight:eh,paddingBottom:eh,paddingLeft:eh,margin:eh,marginTop:eh,marginRight:eh,marginBottom:eh,marginLeft:eh,backgroundPositionX:eh,backgroundPositionY:eh,rotate:ed,rotateX:ed,rotateY:ed,rotateZ:ed,scale:Q,scaleX:Q,scaleY:Q,scaleZ:Q,skew:ed,skewX:ed,skewY:ed,distance:eh,translateX:eh,translateY:eh,translateZ:eh,x:eh,y:eh,z:eh,perspective:eh,transformPerspective:eh,opacity:J,originX:ef,originY:ef,originZ:eh,zIndex:rB,fillOpacity:J,strokeOpacity:J,numOctaves:rB},rz={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},rW=w.length;function rH(e,t,i){let{style:n,vars:r,transformOrigin:s}=e,a=!1,o=!1;for(let e in t){let i=t[e];if(j.has(e)){a=!0;continue}if(X(e)){r[e]=i;continue}{let t=r$(i,rU[e]);e.startsWith("origin")?(o=!0,s[e]=t):n[e]=t}}if(!t.transform&&(a||i?n.transform=function(e,t,i){let n="",r=!0;for(let s=0;s<rW;s++){let a=w[s],o=e[a];if(void 0===o)continue;let l=!0;if(!(l="number"==typeof o?o===+!!a.startsWith("scale"):0===parseFloat(o))||i){let e=r$(o,rU[a]);if(!l){r=!1;let t=rz[a]||a;n+=`${t}(${e}) `}i&&(t[a]=e)}}return n=n.trim(),i?n=i(t,r?"":n):r&&(n="none"),n}(t,e.transform,i):n.transform&&(n.transform="none")),o){let{originX:e="50%",originY:t="50%",originZ:i=0}=s;n.transformOrigin=`${e} ${t} ${i}`}}let rq=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function rX(e,t,i){for(let n in t)D(t[n])||rL(n,i)||(e[n]=t[n])}let rK={offset:"stroke-dashoffset",array:"stroke-dasharray"},rG={offset:"strokeDashoffset",array:"strokeDasharray"};function rY(e,{attrX:t,attrY:i,attrScale:n,pathLength:r,pathSpacing:s=1,pathOffset:a=0,...o},l,u,d){if(rH(e,o,u),l){e.style.viewBox&&(e.attrs.viewBox=e.style.viewBox);return}e.attrs=e.style,e.style={};let{attrs:c,style:h}=e;c.transform&&(h.transform=c.transform,delete c.transform),(h.transform||c.transformOrigin)&&(h.transformOrigin=c.transformOrigin??"50% 50%",delete c.transformOrigin),h.transform&&(h.transformBox=d?.transformBox??"fill-box",delete c.transformBox),void 0!==t&&(c.x=t),void 0!==i&&(c.y=i),void 0!==n&&(c.scale=n),void 0!==r&&function(e,t,i=1,n=0,r=!0){e.pathLength=1;let s=r?rK:rG;e[s.offset]=eh.transform(-n);let a=eh.transform(t),o=eh.transform(i);e[s.array]=`${a} ${o}`}(c,r,s,a,!1)}let rZ=()=>({...rq(),attrs:{}}),rJ=e=>"string"==typeof e&&"svg"===e.toLowerCase(),rQ=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function r0(e){return e.startsWith("while")||e.startsWith("drag")&&"draggable"!==e||e.startsWith("layout")||e.startsWith("onTap")||e.startsWith("onPan")||e.startsWith("onLayout")||rQ.has(e)}let r1=e=>!r0(e);try{!function(e){"function"==typeof e&&(r1=t=>t.startsWith("on")?!r0(t):e(t))}(require("@emotion/is-prop-valid").default)}catch{}let r2=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function r3(e){if("string"!=typeof e||e.includes("-"));else if(r2.indexOf(e)>-1||/[A-Z]/u.test(e))return!0;return!1}function r5(e){let t=(0,nt.useRef)(null);return null===t.current&&(t.current=e()),t.current}let r4=e=>(t,i)=>{let n=(0,nt.useContext)(rC),r=(0,nt.useContext)(ni),s=()=>(function({scrapeMotionValuesFromProps:e,createRenderState:t},i,n,r){return{latestValues:function(e,t,i,n){let r={},s=n(e,{});for(let e in s)r[e]=ny(s[e]);let{initial:a,animate:l}=e,d=r_(e),c=rM(e);t&&c&&!d&&!1!==e.inherit&&(void 0===a&&(a=t.initial),void 0===l&&(l=t.animate));let h=!!i&&!1===i.initial,p=(h=h||!1===a)?l:a;if(p&&"boolean"!=typeof p&&!o(p)){let t=Array.isArray(p)?p:[p];for(let i=0;i<t.length;i++){let n=u(e,t[i]);if(n){let{transitionEnd:e,transition:t,...i}=n;for(let e in i){let t=i[e];if(Array.isArray(t)){let e=h?t.length-1:0;t=t[e]}null!==t&&(r[e]=t)}for(let t in e)r[t]=e[t]}}}return r}(i,n,r,e),renderState:t()}})(e,t,n,r);return i?s():r5(s)};function r6(e,t,i){let{style:n}=e,r={};for(let s in n)(D(n[s])||t.style&&D(t.style[s])||rL(s,e)||i?.getValue(s)?.liveStyle!==void 0)&&(r[s]=n[s]);return r}let r8={useVisualState:r4({scrapeMotionValuesFromProps:r6,createRenderState:rq})};function r9(e,t,i){let n=r6(e,t,i);for(let i in e)(D(e[i])||D(t[i]))&&(n[-1!==w.indexOf(i)?"attr"+i.charAt(0).toUpperCase()+i.substring(1):i]=e[i]);return n}let r7={useVisualState:r4({scrapeMotionValuesFromProps:r9,createRenderState:rZ})},se=e=>t=>t.test(e),st=[Z,eh,ec,ed,em,ep,{test:e=>"auto"===e,parse:e=>e}],si=e=>st.find(se(e)),sn=e=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(e),sr=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u,ss=e=>/^0[^.\s]+$/u.test(e),sa=new Set(["brightness","contrast","saturate","opacity"]);function so(e){let[t,i]=e.slice(0,-1).split("(");if("drop-shadow"===t)return e;let[n]=i.match(et)||[];if(!n)return e;let r=i.replace(n,""),s=+!!sa.has(t);return n!==i&&(s*=100),t+"("+s+r+")"}let sl=/\b([a-z-]*)\(.*?\)/gu,su={...eT,getAnimatableNone:e=>{let t=e.match(sl);return t?t.map(so).join(" "):e}},sd={...rU,color:ey,backgroundColor:ey,outlineColor:ey,fill:ey,stroke:ey,borderColor:ey,borderTopColor:ey,borderRightColor:ey,borderBottomColor:ey,borderLeftColor:ey,filter:su,WebkitFilter:su},sc=e=>sd[e];function sh(e,t){let i=sc(e);return i!==su&&(i=eT),i.getAnimatableNone?i.getAnimatableNone(t):void 0}let sp=new Set(["auto","none","0"]);class sm extends tF{constructor(e,t,i,n,r){super(e,t,i,n,r,!0)}readKeyframes(){let{unresolvedKeyframes:e,element:t,name:i}=this;if(!t||!t.current)return;super.readKeyframes();for(let i=0;i<e.length;i++){let n=e[i];if("string"==typeof n&&G(n=n.trim())){let r=function e(t,i,n=1){H(n<=4,`Max CSS variable fallback depth detected in property "${t}". This may indicate a circular fallback dependency.`);let[r,s]=function(e){let t=sr.exec(e);if(!t)return[,];let[,i,n,r]=t;return[`--${i??n}`,r]}(t);if(!r)return;let a=window.getComputedStyle(i).getPropertyValue(r);if(a){let e=a.trim();return sn(e)?parseFloat(e):e}return G(s)?e(s,i,n+1):s}(n,t.current);void 0!==r&&(e[i]=r),i===e.length-1&&(this.finalKeyframe=n)}}if(this.resolveNoneKeyframes(),!P.has(i)||2!==e.length)return;let[n,r]=e,s=si(n),a=si(r);if(s!==a)if(tR(s)&&tR(a))for(let t=0;t<e.length;t++){let i=e[t];"string"==typeof i&&(e[t]=parseFloat(i))}else t_[i]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){let{unresolvedKeyframes:e,name:t}=this,i=[];for(let t=0;t<e.length;t++){var n;(null===e[t]||("number"==typeof(n=e[t])?0===n:null===n||"none"===n||"0"===n||ss(n)))&&i.push(t)}i.length&&function(e,t,i){let n,r=0;for(;r<e.length&&!n;){let t=e[r];"string"==typeof t&&!sp.has(t)&&ej(t).values.length&&(n=e[r]),r++}if(n&&i)for(let r of t)e[r]=sh(i,n)}(e,i,t)}measureInitialState(){let{element:e,unresolvedKeyframes:t,name:i}=this;if(!e||!e.current)return;"height"===i&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=t_[i](e.measureViewportBox(),window.getComputedStyle(e.current)),t[0]=this.measuredOrigin;let n=t[t.length-1];void 0!==n&&e.getValue(i,n).jump(n,!1)}measureEndState(){let{element:e,name:t,unresolvedKeyframes:i}=this;if(!e||!e.current)return;let n=e.getValue(t);n&&n.jump(this.measuredOrigin,!1);let r=i.length-1,s=i[r];i[r]=t_[t](e.measureViewportBox(),window.getComputedStyle(e.current)),null!==s&&void 0===this.finalKeyframe&&(this.finalKeyframe=s),this.removedTransforms?.length&&this.removedTransforms.forEach(([t,i])=>{e.getValue(t).set(i)}),this.resolveNoneKeyframes()}}let sf=[...st,ey,eT],sg=e=>sf.find(se(e)),sy={current:null},sv={current:!1},sx=new WeakMap,sb=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class sw{scrapeMotionValuesFromProps(e,t,i){return{}}constructor({parent:e,props:t,presenceContext:i,reducedMotionConfig:n,blockInitialAnimation:r,visualState:s},a={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=tF,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{let e=R.now();this.renderScheduledAt<e&&(this.renderScheduledAt=e,y.render(this.render,!1,!0))};let{latestValues:o,renderState:l}=s;this.latestValues=o,this.baseTarget={...o},this.initialValues=t.initial?{...o}:{},this.renderState=l,this.parent=e,this.props=t,this.presenceContext=i,this.depth=e?e.depth+1:0,this.reducedMotionConfig=n,this.options=a,this.blockInitialAnimation=!!r,this.isControllingVariants=r_(t),this.isVariantNode=rM(t),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(e&&e.current);let{willChange:u,...d}=this.scrapeMotionValuesFromProps(t,{},this);for(let e in d){let t=d[e];void 0!==o[e]&&D(t)&&t.set(o[e],!1)}}mount(e){this.current=e,sx.set(e,this),this.projection&&!this.projection.instance&&this.projection.mount(e),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((e,t)=>this.bindToMotionValue(t,e)),sv.current||function(){if(sv.current=!0,rD)if(window.matchMedia){let e=window.matchMedia("(prefers-reduced-motion)"),t=()=>sy.current=e.matches;e.addEventListener("change",t),t()}else sy.current=!1}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||sy.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){for(let e in this.projection&&this.projection.unmount(),v(this.notifyUpdate),v(this.render),this.valueSubscriptions.forEach(e=>e()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this),this.events)this.events[e].clear();for(let e in this.features){let t=this.features[e];t&&(t.unmount(),t.isMounted=!1)}this.current=null}bindToMotionValue(e,t){let i;this.valueSubscriptions.has(e)&&this.valueSubscriptions.get(e)();let n=j.has(e);n&&this.onBindTransform&&this.onBindTransform();let r=t.on("change",t=>{this.latestValues[e]=t,this.props.onUpdate&&y.preRender(this.notifyUpdate),n&&this.projection&&(this.projection.isTransformDirty=!0)}),s=t.on("renderRequest",this.scheduleRender);window.MotionCheckAppearSync&&(i=window.MotionCheckAppearSync(this,e,t)),this.valueSubscriptions.set(e,()=>{r(),s(),i&&i(),t.owner&&t.stop()})}sortNodePosition(e){return this.current&&this.sortInstanceNodePosition&&this.type===e.type?this.sortInstanceNodePosition(this.current,e.current):0}updateFeatures(){let e="animation";for(e in rV){let t=rV[e];if(!t)continue;let{isEnabled:i,Feature:n}=t;if(!this.features[e]&&n&&i(this.props)&&(this.features[e]=new n(this)),this.features[e]){let t=this.features[e];t.isMounted?t.update():(t.mount(),t.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):iN()}getStaticValue(e){return this.latestValues[e]}setStaticValue(e,t){this.latestValues[e]=t}update(e,t){(e.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=e,this.prevPresenceContext=this.presenceContext,this.presenceContext=t;for(let t=0;t<sb.length;t++){let i=sb[t];this.propEventSubscriptions[i]&&(this.propEventSubscriptions[i](),delete this.propEventSubscriptions[i]);let n=e["on"+i];n&&(this.propEventSubscriptions[i]=this.on(i,n))}this.prevMotionValues=function(e,t,i){for(let n in t){let r=t[n],s=i[n];if(D(r))e.addValue(n,r);else if(D(s))e.addValue(n,M(r,{owner:e}));else if(s!==r)if(e.hasValue(n)){let t=e.getValue(n);!0===t.liveStyle?t.jump(r):t.hasAnimated||t.set(r)}else{let t=e.getStaticValue(n);e.addValue(n,M(void 0!==t?t:r,{owner:e}))}}for(let n in i)void 0===t[n]&&e.removeValue(n);return t}(this,this.scrapeMotionValuesFromProps(e,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(e){return this.props.variants?this.props.variants[e]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(e){let t=this.getClosestVariantNode();if(t)return t.variantChildren&&t.variantChildren.add(e),()=>t.variantChildren.delete(e)}addValue(e,t){let i=this.values.get(e);t!==i&&(i&&this.removeValue(e),this.bindToMotionValue(e,t),this.values.set(e,t),this.latestValues[e]=t.get())}removeValue(e){this.values.delete(e);let t=this.valueSubscriptions.get(e);t&&(t(),this.valueSubscriptions.delete(e)),delete this.latestValues[e],this.removeValueFromRenderState(e,this.renderState)}hasValue(e){return this.values.has(e)}getValue(e,t){if(this.props.values&&this.props.values[e])return this.props.values[e];let i=this.values.get(e);return void 0===i&&void 0!==t&&(i=M(null===t?void 0:t,{owner:this}),this.addValue(e,i)),i}readValue(e,t){let i=void 0===this.latestValues[e]&&this.current?this.getBaseTargetFromProps(this.props,e)??this.readValueFromInstance(this.current,e,this.options):this.latestValues[e];return null!=i&&("string"==typeof i&&(sn(i)||ss(i))?i=parseFloat(i):!sg(i)&&eT.test(t)&&(i=sh(e,t)),this.setBaseTarget(e,D(i)?i.get():i)),D(i)?i.get():i}setBaseTarget(e,t){this.baseTarget[e]=t}getBaseTarget(e){let t,{initial:i}=this.props;if("string"==typeof i||"object"==typeof i){let n=u(this.props,i,this.presenceContext?.custom);n&&(t=n[e])}if(i&&void 0!==t)return t;let n=this.getBaseTargetFromProps(this.props,e);return void 0===n||D(n)?void 0!==this.initialValues[e]&&void 0===t?void 0:this.baseTarget[e]:n}on(e,t){return this.events[e]||(this.events[e]=new T),this.events[e].add(t)}notify(e,...t){this.events[e]&&this.events[e].notify(...t)}}class sj extends sw{constructor(){super(...arguments),this.KeyframeResolver=sm}sortInstanceNodePosition(e,t){return 2&e.compareDocumentPosition(t)?1:-1}getBaseTargetFromProps(e,t){return e.style?e.style[t]:void 0}removeValueFromRenderState(e,{vars:t,style:i}){delete t[e],delete i[e]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:e}=this.props;D(e)&&(this.childSubscription=e.on("change",e=>{this.current&&(this.current.textContent=`${e}`)}))}}function sP(e,{style:t,vars:i},n,r){let s,a=e.style;for(s in t)a[s]=t[s];for(s in r?.applyProjectionStyles(a,n),i)a.setProperty(s,i[s])}class sS extends sj{constructor(){super(...arguments),this.type="html",this.renderInstance=sP}readValueFromInstance(e,t){if(j.has(t))return this.projection?.isProjecting?tS(t):tT(e,t);{let i=window.getComputedStyle(e),n=(X(t)?i.getPropertyValue(t):i[t])||0;return"string"==typeof n?n.trim():n}}measureInstanceViewportBox(e,{transformPagePoint:t}){return iH(e,t)}build(e,t,i){rH(e,t,i.transformTemplate)}scrapeMotionValuesFromProps(e,t,i){return r6(e,t,i)}}let sE=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);class sT extends sj{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=iN}getBaseTargetFromProps(e,t){return e[t]}readValueFromInstance(e,t){if(j.has(t)){let e=sc(t);return e&&e.default||0}return t=sE.has(t)?t:V(t),e.getAttribute(t)}scrapeMotionValuesFromProps(e,t,i){return r9(e,t,i)}build(e,t,i){rY(e,t,this.isSVGTag,i.transformTemplate,i.style)}renderInstance(e,t,i,n){for(let i in sP(e,t,void 0,n),t.attrs)e.setAttribute(sE.has(i)?i:V(i),t.attrs[i])}mount(e){this.isSVGTag=rJ(e.tagName),super.mount(e)}}let sA=function(e){if("undefined"==typeof Proxy)return e;let t=new Map;return new Proxy((...t)=>e(...t),{get:(i,n)=>"create"===n?e:(t.has(n)||t.set(n,e(n)),t.get(n))})}((r={animation:{Feature:ip},exit:{Feature:ig},inView:{Feature:rA},tap:{Feature:rw},focus:{Feature:rh},hover:{Feature:rc},pan:{Feature:i7},drag:{Feature:i8,ProjectionNode:ro,MeasureLayout:nh},layout:{ProjectionNode:ro,MeasureLayout:nh}},s=(e,t)=>r3(e)?new sT(t):new sS(t,{allowProjection:e!==nt.Fragment}),function(e,{forwardMotionProps:t}={forwardMotionProps:!1}){return function({preloadedFeatures:e,createVisualElement:t,useRender:i,useVisualState:n,Component:r}){function s(e,s){var o,l,u;let d,c={...(0,nt.useContext)(rk),...e,layoutId:function({layoutId:e}){let t=(0,nt.useContext)(nr).id;return t&&void 0!==e?t+"-"+e:e}(e)},{isStatic:h}=c,p=function(e){let{initial:t,animate:i}=function(e,t){if(r_(e)){let{initial:t,animate:i}=e;return{initial:!1===t||ir(t)?t:void 0,animate:ir(i)?i:void 0}}return!1!==e.inherit?t:{}}(e,(0,nt.useContext)(rC));return(0,nt.useMemo)(()=>({initial:t,animate:i}),[rN(t),rN(i)])}(e),m=n(e,h);if(!h&&rD){l=0,u=0,(0,nt.useContext)(rR).strict;let e=function(e){let{drag:t,layout:i}=rV;if(!t&&!i)return{};let n={...t,...i};return{MeasureLayout:t?.isEnabled(e)||i?.isEnabled(e)?n.MeasureLayout:void 0,ProjectionNode:n.ProjectionNode}}(c);d=e.MeasureLayout,p.visualElement=function(e,t,i,n,r){let{visualElement:s}=(0,nt.useContext)(rC),a=(0,nt.useContext)(rR),o=(0,nt.useContext)(ni),l=(0,nt.useContext)(rk).reducedMotion,u=(0,nt.useRef)(null);n=n||a.renderer,!u.current&&n&&(u.current=n(e,{visualState:t,parent:s,props:i,presenceContext:o,blockInitialAnimation:!!o&&!1===o.initial,reducedMotionConfig:l}));let d=u.current,c=(0,nt.useContext)(ns);d&&!d.projection&&r&&("html"===d.type||"svg"===d.type)&&function(e,t,i,n){let{layoutId:r,layout:s,drag:a,dragConstraints:o,layoutScroll:l,layoutRoot:u,layoutCrossfade:d}=t;e.projection=new i(e.latestValues,t["data-framer-portal-id"]?void 0:function e(t){if(t)return!1!==t.options.allowProjection?t.projection:e(t.parent)}(e.parent)),e.projection.setOptions({layoutId:r,layout:s,alwaysMeasureLayout:!!a||o&&iX(o),visualElement:e,animationType:"string"==typeof s?s:"both",initialPromotionConfig:n,crossfade:d,layoutScroll:l,layoutRoot:u})}(u.current,i,r,c);let h=(0,nt.useRef)(!1);(0,nt.useInsertionEffect)(()=>{d&&h.current&&d.update(i,o)});let p=i[I],m=(0,nt.useRef)(!!p&&!window.MotionHandoffIsComplete?.(p)&&window.MotionHasOptimisedAnimation?.(p));return rF(()=>{d&&(h.current=!0,window.MotionIsMounted=!0,d.updateFeatures(),ne.render(d.render),m.current&&d.animationState&&d.animationState.animateChanges())}),(0,nt.useEffect)(()=>{d&&(!m.current&&d.animationState&&d.animationState.animateChanges(),m.current&&(queueMicrotask(()=>{window.MotionHandoffMarkAsComplete?.(p)}),m.current=!1))}),d}(r,m,c,t,e.ProjectionNode)}return(0,a.jsxs)(rC.Provider,{value:p,children:[d&&p.visualElement?(0,a.jsx)(d,{visualElement:p.visualElement,...c}):null,i(r,e,(o=p.visualElement,(0,nt.useCallback)(e=>{e&&m.onMount&&m.onMount(e),o&&(e?o.mount(e):o.unmount()),s&&("function"==typeof s?s(e):iX(s)&&(s.current=e))},[o])),m,h,p.visualElement)]})}e&&function(e){for(let t in e)rV[t]={...rV[t],...e[t]}}(e),s.displayName=`motion.${"string"==typeof r?r:`create(${r.displayName??r.name??""})`}`;let o=(0,nt.forwardRef)(s);return o[rI]=r,o}({...r3(e)?r7:r8,preloadedFeatures:r,useRender:function(e=!1){return(t,i,n,{latestValues:r},s)=>{let a=(r3(t)?function(e,t,i,n){let r=(0,nt.useMemo)(()=>{let i=rZ();return rY(i,t,rJ(n),e.transformTemplate,e.style),{...i.attrs,style:{...i.style}}},[t]);if(e.style){let t={};rX(t,e.style,e),r.style={...t,...r.style}}return r}:function(e,t){let i={},n=function(e,t){let i=e.style||{},n={};return rX(n,i,e),Object.assign(n,function({transformTemplate:e},t){return(0,nt.useMemo)(()=>{let i=rq();return rH(i,t,e),Object.assign({},i.vars,i.style)},[t])}(e,t)),n}(e,t);return e.drag&&!1!==e.dragListener&&(i.draggable=!1,n.userSelect=n.WebkitUserSelect=n.WebkitTouchCallout="none",n.touchAction=!0===e.drag?"none":`pan-${"x"===e.drag?"y":"x"}`),void 0===e.tabIndex&&(e.onTap||e.onTapStart||e.whileTap)&&(i.tabIndex=0),i.style=n,i})(i,r,s,t),o=function(e,t,i){let n={};for(let r in e)("values"!==r||"object"!=typeof e.values)&&(r1(r)||!0===i&&r0(r)||!t&&!r0(r)||e.draggable&&r.startsWith("onDrag"))&&(n[r]=e[r]);return n}(i,"string"==typeof t,e),l=t!==nt.Fragment?{...o,...a,ref:n}:{},{children:u}=i,d=(0,nt.useMemo)(()=>D(u)?u.get():u,[u]);return(0,nt.createElement)(t,{...l,children:d})}}(t),createVisualElement:s,Component:e})}));function sR(){return(0,a.jsxs)("section",{className:"min-h-screen flex items-center justify-center relative overflow-hidden",children:[(0,a.jsxs)("div",{className:"absolute inset-0 opacity-10",children:[(0,a.jsx)("div",{className:"absolute top-10 left-10 w-20 h-20 bg-rose-300 rounded-full"}),(0,a.jsx)("div",{className:"absolute top-32 right-20 w-16 h-16 bg-pink-300 rounded-full"}),(0,a.jsx)("div",{className:"absolute bottom-20 left-20 w-24 h-24 bg-rose-200 rounded-full"}),(0,a.jsx)("div",{className:"absolute bottom-32 right-10 w-12 h-12 bg-pink-200 rounded-full"})]}),(0,a.jsxs)("div",{className:"text-center z-10 px-4",children:[(0,a.jsx)(sA.div,{initial:{y:-50,opacity:0},animate:{y:0,opacity:1},transition:{duration:1,ease:"easeOut"},children:(0,a.jsx)("h1",{className:"text-4xl md:text-6xl font-bold text-gray-800 mb-4",children:"Sarah & Ahmad"})}),(0,a.jsx)(sA.div,{initial:{scale:0},animate:{scale:1},transition:{delay:.5,duration:.8,ease:"easeOut"},className:"my-8",children:(0,a.jsx)("div",{className:"text-8xl mb-4",children:"\uD83D\uDC95"})}),(0,a.jsxs)(sA.div,{initial:{y:50,opacity:0},animate:{y:0,opacity:1},transition:{delay:1,duration:1,ease:"easeOut"},children:[(0,a.jsx)("p",{className:"text-xl md:text-2xl text-gray-600 mb-6",children:"Dengan penuh sukacita, kami mengundang Anda"}),(0,a.jsx)("p",{className:"text-lg md:text-xl text-gray-500 mb-8",children:"untuk merayakan pernikahan kami"})]}),(0,a.jsxs)(sA.div,{initial:{opacity:0},animate:{opacity:1},transition:{delay:1.5,duration:1},className:"text-lg text-gray-600",children:[(0,a.jsx)("p",{className:"mb-2",children:"Sabtu, 15 Juli 2024"}),(0,a.jsx)("p",{children:"Pukul 09.00 WIB"})]}),(0,a.jsx)(sA.div,{initial:{y:30,opacity:0},animate:{y:0,opacity:1},transition:{delay:2,duration:.8},className:"mt-12",children:(0,a.jsx)(sA.button,{whileHover:{scale:1.05},whileTap:{scale:.95},onClick:()=>{document.getElementById("couple-info")?.scrollIntoView({behavior:"smooth"})},className:"bg-gradient-to-r from-rose-400 to-pink-500 text-white px-8 py-3 rounded-full font-semibold shadow-lg hover:shadow-xl transition-all duration-300",children:"Lihat Detail"})})]}),(0,a.jsx)(sA.div,{animate:{y:[-20,-40,-20],rotate:[0,10,-10,0]},transition:{duration:4,repeat:1/0,ease:"easeInOut"},className:"absolute top-20 left-1/4 text-4xl opacity-30",children:"\uD83D\uDC96"}),(0,a.jsx)(sA.div,{animate:{y:[-30,-50,-30],rotate:[0,-10,10,0]},transition:{duration:5,repeat:1/0,ease:"easeInOut",delay:1},className:"absolute top-40 right-1/4 text-3xl opacity-30",children:"\uD83D\uDC9D"})]})}function sk(){let e={hidden:{y:50,opacity:0},visible:{y:0,opacity:1,transition:{duration:.8}}};return(0,a.jsx)("section",{id:"couple-info",className:"py-20 px-4",children:(0,a.jsx)("div",{className:"max-w-6xl mx-auto",children:(0,a.jsxs)(sA.div,{initial:"hidden",whileInView:"visible",viewport:{once:!0,amount:.3},variants:{hidden:{opacity:0},visible:{opacity:1,transition:{staggerChildren:.3}}},children:[(0,a.jsx)(sA.h2,{variants:e,className:"text-4xl md:text-5xl font-bold text-center text-gray-800 mb-16",children:"Mempelai"}),(0,a.jsxs)("div",{className:"grid md:grid-cols-2 gap-12 items-center",children:[(0,a.jsxs)(sA.div,{variants:e,className:"text-center",children:[(0,a.jsxs)("div",{className:"relative mb-8",children:[(0,a.jsx)(sA.div,{whileHover:{scale:1.05},className:"w-64 h-64 mx-auto bg-gradient-to-br from-rose-200 to-pink-300 rounded-full flex items-center justify-center shadow-2xl",children:(0,a.jsx)("div",{className:"text-8xl",children:"\uD83D\uDC70\uD83C\uDFFB"})}),(0,a.jsx)(sA.div,{animate:{rotate:360},transition:{duration:20,repeat:1/0,ease:"linear"},className:"absolute -top-4 -right-4 w-16 h-16 bg-rose-400 rounded-full flex items-center justify-center text-2xl",children:"\uD83C\uDF38"})]}),(0,a.jsx)("h3",{className:"text-3xl font-bold text-gray-800 mb-4",children:"Sarah Putri"}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"Putri pertama dari"}),(0,a.jsx)("p",{className:"text-lg font-semibold text-gray-700 mb-2",children:"Bapak Suharto & Ibu Siti Aminah"}),(0,a.jsx)("p",{className:"text-gray-500 text-sm",children:"Jakarta, Indonesia"})]}),(0,a.jsx)(sA.div,{variants:e,className:"flex justify-center md:hidden",children:(0,a.jsx)(sA.div,{animate:{scale:[1,1.2,1],rotate:[0,5,-5,0]},transition:{duration:2,repeat:1/0,ease:"easeInOut"},className:"text-6xl",children:"\uD83D\uDC95"})}),(0,a.jsxs)(sA.div,{variants:e,className:"text-center",children:[(0,a.jsxs)("div",{className:"relative mb-8",children:[(0,a.jsx)(sA.div,{whileHover:{scale:1.05},className:"w-64 h-64 mx-auto bg-gradient-to-br from-blue-200 to-indigo-300 rounded-full flex items-center justify-center shadow-2xl",children:(0,a.jsx)("div",{className:"text-8xl",children:"\uD83E\uDD35\uD83C\uDFFB"})}),(0,a.jsx)(sA.div,{animate:{rotate:-360},transition:{duration:25,repeat:1/0,ease:"linear"},className:"absolute -top-4 -left-4 w-16 h-16 bg-blue-400 rounded-full flex items-center justify-center text-2xl",children:"⭐"})]}),(0,a.jsx)("h3",{className:"text-3xl font-bold text-gray-800 mb-4",children:"Ahmad Rizki"}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"Putra kedua dari"}),(0,a.jsx)("p",{className:"text-lg font-semibold text-gray-700 mb-2",children:"Bapak Ahmad Yusuf & Ibu Fatimah"}),(0,a.jsx)("p",{className:"text-gray-500 text-sm",children:"Bandung, Indonesia"})]})]}),(0,a.jsx)(sA.div,{variants:e,className:"hidden md:flex justify-center mt-16",children:(0,a.jsx)(sA.div,{animate:{scale:[1,1.3,1],rotate:[0,10,-10,0]},transition:{duration:3,repeat:1/0,ease:"easeInOut"},className:"text-8xl",children:"\uD83D\uDC95"})}),(0,a.jsxs)(sA.div,{variants:e,className:"text-center mt-16 max-w-2xl mx-auto",children:[(0,a.jsx)("blockquote",{className:"text-xl md:text-2xl text-gray-600 italic mb-4",children:"“Dan di antara tanda-tanda kekuasaan-Nya ialah Dia menciptakan untukmu isteri-isteri dari jenismu sendiri, supaya kamu cenderung dan merasa tenteram kepadanya, dan dijadikan-Nya diantaramu rasa kasih dan sayang.”"}),(0,a.jsx)("cite",{className:"text-gray-500 font-semibold",children:"- QS. Ar-Rum: 21"})]})]})})})}function sC(){let e={hidden:{y:50,opacity:0},visible:{y:0,opacity:1,transition:{duration:.8}}};return(0,a.jsx)("section",{className:"py-20 px-4 bg-gradient-to-br from-rose-50 to-pink-50",children:(0,a.jsx)("div",{className:"max-w-6xl mx-auto",children:(0,a.jsxs)(sA.div,{initial:"hidden",whileInView:"visible",viewport:{once:!0,amount:.3},variants:{hidden:{opacity:0},visible:{opacity:1,transition:{staggerChildren:.2}}},children:[(0,a.jsx)(sA.h2,{variants:e,className:"text-4xl md:text-5xl font-bold text-center text-gray-800 mb-16",children:"Detail Acara"}),(0,a.jsx)("div",{className:"grid md:grid-cols-2 gap-8",children:[{title:"Akad Nikah",date:"Sabtu, 15 Juli 2024",time:"09.00 - 11.00 WIB",location:"Masjid Al-Ikhlas",address:"Jl. Merdeka No. 123, Jakarta Pusat",icon:"\uD83D\uDD4C",color:"from-emerald-400 to-teal-500"},{title:"Resepsi Pernikahan",date:"Sabtu, 15 Juli 2024",time:"18.00 - 21.00 WIB",location:"Gedung Serbaguna Melati",address:"Jl. Bunga Raya No. 456, Jakarta Pusat",icon:"\uD83C\uDF89",color:"from-rose-400 to-pink-500"}].map((t,i)=>(0,a.jsxs)(sA.div,{variants:e,whileHover:{y:-10},className:"bg-white rounded-3xl shadow-xl p-8 relative overflow-hidden",children:[(0,a.jsx)("div",{className:"absolute top-0 right-0 w-32 h-32 opacity-10",children:(0,a.jsx)("div",{className:`w-full h-full bg-gradient-to-br ${t.color} rounded-full transform translate-x-8 -translate-y-8`})}),(0,a.jsxs)("div",{className:"relative z-10",children:[(0,a.jsxs)("div",{className:"text-center mb-6",children:[(0,a.jsx)(sA.div,{animate:{rotate:[0,10,-10,0],scale:[1,1.1,1]},transition:{duration:3,repeat:1/0,ease:"easeInOut"},className:"text-6xl mb-4",children:t.icon}),(0,a.jsx)("h3",{className:"text-2xl font-bold text-gray-800 mb-2",children:t.title})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"text-2xl mr-3",children:"\uD83D\uDCC5"}),(0,a.jsx)("div",{children:(0,a.jsx)("p",{className:"font-semibold text-gray-700",children:t.date})})]}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"text-2xl mr-3",children:"⏰"}),(0,a.jsx)("div",{children:(0,a.jsx)("p",{className:"font-semibold text-gray-700",children:t.time})})]}),(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)("div",{className:"text-2xl mr-3 mt-1",children:"\uD83D\uDCCD"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-semibold text-gray-700 mb-1",children:t.location}),(0,a.jsx)("p",{className:"text-gray-600 text-sm",children:t.address})]})]})]}),(0,a.jsx)(sA.button,{whileHover:{scale:1.05},whileTap:{scale:.95},className:`w-full mt-6 bg-gradient-to-r ${t.color} text-white py-3 rounded-full font-semibold shadow-lg hover:shadow-xl transition-all duration-300`,onClick:()=>{let e=encodeURIComponent(t.address);window.open(`https://maps.google.com/maps?q=${e}`,"_blank")},children:"Lihat Lokasi"})]})]},i))}),(0,a.jsxs)(sA.div,{variants:e,className:"mt-16 text-center",children:[(0,a.jsx)("h3",{className:"text-2xl font-bold text-gray-800 mb-8",children:"Menuju Hari Bahagia"}),(0,a.jsx)("div",{className:"grid grid-cols-4 gap-4 max-w-md mx-auto",children:["Hari","Jam","Menit","Detik"].map((e,t)=>(0,a.jsxs)(sA.div,{whileHover:{scale:1.05},className:"bg-white rounded-2xl shadow-lg p-4",children:[(0,a.jsx)(sA.div,{animate:{scale:[1,1.1,1]},transition:{duration:2,repeat:1/0,delay:.2*t},className:"text-2xl font-bold text-rose-500 mb-1",children:0===t?"30":1===t?"12":2===t?"45":"30"}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:e})]},e))})]})]})})})}function s_(){let[e,t]=(0,nt.useState)(null),i={hidden:{opacity:0},visible:{opacity:1,transition:{staggerChildren:.1}}},n={hidden:{scale:.8,opacity:0},visible:{scale:1,opacity:1,transition:{duration:.6}}},r=[{id:1,emoji:"\uD83D\uDC91",title:"Foto Prewedding 1"},{id:2,emoji:"\uD83D\uDC6B",title:"Foto Prewedding 2"},{id:3,emoji:"\uD83D\uDC95",title:"Foto Prewedding 3"},{id:4,emoji:"\uD83C\uDF39",title:"Foto Prewedding 4"},{id:5,emoji:"\uD83D\uDC96",title:"Foto Prewedding 5"},{id:6,emoji:"\uD83C\uDF8A",title:"Foto Prewedding 6"}];return(0,a.jsx)("section",{className:"py-20 px-4",children:(0,a.jsx)("div",{className:"max-w-6xl mx-auto",children:(0,a.jsxs)(sA.div,{initial:"hidden",whileInView:"visible",viewport:{once:!0,amount:.3},variants:i,children:[(0,a.jsx)(sA.h2,{variants:n,className:"text-4xl md:text-5xl font-bold text-center text-gray-800 mb-16",children:"Galeri Foto"}),(0,a.jsx)(sA.p,{variants:n,className:"text-center text-gray-600 mb-12 max-w-2xl mx-auto",children:"Beberapa momen indah perjalanan cinta kami yang ingin kami bagikan dengan Anda"}),(0,a.jsx)(sA.div,{variants:i,className:"grid grid-cols-2 md:grid-cols-3 gap-4",children:r.map((e,i)=>(0,a.jsxs)(sA.div,{variants:n,whileHover:{scale:1.05,rotate:i%2==0?2:-2},whileTap:{scale:.95},onClick:()=>t(i),className:"relative aspect-square bg-gradient-to-br from-rose-200 to-pink-300 rounded-2xl shadow-lg cursor-pointer overflow-hidden group",children:[(0,a.jsx)("div",{className:"absolute inset-0 flex items-center justify-center",children:(0,a.jsx)(sA.div,{animate:{scale:[1,1.2,1],rotate:[0,5,-5,0]},transition:{duration:3,repeat:1/0,ease:"easeInOut",delay:.2*i},className:"text-6xl md:text-8xl",children:e.emoji})}),(0,a.jsx)("div",{className:"absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300 flex items-end",children:(0,a.jsx)("div",{className:"p-4 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-300",children:(0,a.jsx)("p",{className:"font-semibold",children:e.title})})})]},e.id))}),null!==e&&(0,a.jsx)(sA.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"fixed inset-0 bg-black bg-opacity-80 flex items-center justify-center z-50 p-4",onClick:()=>t(null),children:(0,a.jsxs)(sA.div,{initial:{scale:.8,opacity:0},animate:{scale:1,opacity:1},exit:{scale:.8,opacity:0},className:"bg-white rounded-3xl p-8 max-w-md w-full text-center",onClick:e=>e.stopPropagation(),children:[(0,a.jsx)("div",{className:"text-8xl mb-4",children:r[e].emoji}),(0,a.jsx)("h3",{className:"text-2xl font-bold text-gray-800 mb-4",children:r[e].title}),(0,a.jsx)("p",{className:"text-gray-600 mb-6",children:"Momen indah dalam perjalanan cinta kami"}),(0,a.jsx)(sA.button,{whileHover:{scale:1.05},whileTap:{scale:.95},onClick:()=>t(null),className:"bg-gradient-to-r from-rose-400 to-pink-500 text-white px-6 py-2 rounded-full font-semibold",children:"Tutup"})]})}),(0,a.jsxs)(sA.div,{variants:n,className:"text-center mt-16 max-w-2xl mx-auto",children:[(0,a.jsx)(sA.div,{animate:{rotate:[0,5,-5,0],scale:[1,1.1,1]},transition:{duration:4,repeat:1/0,ease:"easeInOut"},className:"text-6xl mb-6",children:"\uD83D\uDC9D"}),(0,a.jsx)("blockquote",{className:"text-xl text-gray-600 italic mb-4",children:"“Cinta sejati bukan tentang menjadi tak terpisahkan; itu tentang menjadi terpisah dan tidak ada yang berubah.”"}),(0,a.jsx)("cite",{className:"text-gray-500 font-semibold",children:"- Sarah & Ahmad"})]})]})})})}function sM(){let[e,t]=(0,nt.useState)({name:"",attendance:"",guests:"1",message:""}),[i,n]=(0,nt.useState)(!1),r={hidden:{y:50,opacity:0},visible:{y:0,opacity:1,transition:{duration:.8}}},s=i=>{t({...e,[i.target.name]:i.target.value})};return i?(0,a.jsx)("section",{className:"py-20 px-4 bg-gradient-to-br from-green-50 to-emerald-50",children:(0,a.jsx)("div",{className:"max-w-2xl mx-auto text-center",children:(0,a.jsxs)(sA.div,{initial:{scale:0,opacity:0},animate:{scale:1,opacity:1},transition:{duration:.8,ease:"easeOut"},className:"bg-white rounded-3xl shadow-xl p-12",children:[(0,a.jsx)(sA.div,{animate:{rotate:[0,10,-10,0],scale:[1,1.2,1]},transition:{duration:2,repeat:1/0,ease:"easeInOut"},className:"text-8xl mb-6",children:"✅"}),(0,a.jsx)("h3",{className:"text-3xl font-bold text-gray-800 mb-4",children:"Terima Kasih!"}),(0,a.jsx)("p",{className:"text-gray-600 text-lg",children:"RSVP Anda telah berhasil dikirim. Kami sangat menantikan kehadiran Anda!"})]})})}):(0,a.jsx)("section",{className:"py-20 px-4 bg-gradient-to-br from-rose-50 to-pink-50",children:(0,a.jsx)("div",{className:"max-w-2xl mx-auto",children:(0,a.jsxs)(sA.div,{initial:"hidden",whileInView:"visible",viewport:{once:!0,amount:.3},variants:{hidden:{opacity:0},visible:{opacity:1,transition:{staggerChildren:.2}}},children:[(0,a.jsx)(sA.h2,{variants:r,className:"text-4xl md:text-5xl font-bold text-center text-gray-800 mb-8",children:"RSVP"}),(0,a.jsx)(sA.p,{variants:r,className:"text-center text-gray-600 mb-12",children:"Mohon konfirmasi kehadiran Anda sebelum tanggal 10 Juli 2024"}),(0,a.jsx)(sA.div,{variants:r,className:"bg-white rounded-3xl shadow-xl p-8",children:(0,a.jsxs)("form",{onSubmit:i=>{i.preventDefault(),console.log("RSVP Data:",e),n(!0),setTimeout(()=>{n(!1),t({name:"",attendance:"",guests:"1",message:""})},3e3)},className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"name",className:"block text-sm font-semibold text-gray-700 mb-2",children:"Nama Lengkap *"}),(0,a.jsx)(sA.input,{whileFocus:{scale:1.02},type:"text",id:"name",name:"name",value:e.name,onChange:s,required:!0,className:"w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-rose-400 focus:border-transparent transition-all duration-300",placeholder:"Masukkan nama lengkap Anda"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"attendance",className:"block text-sm font-semibold text-gray-700 mb-2",children:"Konfirmasi Kehadiran *"}),(0,a.jsxs)(sA.select,{whileFocus:{scale:1.02},id:"attendance",name:"attendance",value:e.attendance,onChange:s,required:!0,className:"w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-rose-400 focus:border-transparent transition-all duration-300",children:[(0,a.jsx)("option",{value:"",children:"Pilih konfirmasi kehadiran"}),(0,a.jsx)("option",{value:"hadir",children:"Ya, saya akan hadir"}),(0,a.jsx)("option",{value:"tidak-hadir",children:"Maaf, saya tidak dapat hadir"})]})]}),"hadir"===e.attendance&&(0,a.jsxs)(sA.div,{initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},transition:{duration:.3},children:[(0,a.jsx)("label",{htmlFor:"guests",className:"block text-sm font-semibold text-gray-700 mb-2",children:"Jumlah Tamu"}),(0,a.jsxs)(sA.select,{whileFocus:{scale:1.02},id:"guests",name:"guests",value:e.guests,onChange:s,className:"w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-rose-400 focus:border-transparent transition-all duration-300",children:[(0,a.jsx)("option",{value:"1",children:"1 orang"}),(0,a.jsx)("option",{value:"2",children:"2 orang"}),(0,a.jsx)("option",{value:"3",children:"3 orang"}),(0,a.jsx)("option",{value:"4",children:"4 orang"}),(0,a.jsx)("option",{value:"5",children:"5+ orang"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"message",className:"block text-sm font-semibold text-gray-700 mb-2",children:"Pesan & Doa"}),(0,a.jsx)(sA.textarea,{whileFocus:{scale:1.02},id:"message",name:"message",value:e.message,onChange:s,rows:4,className:"w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-rose-400 focus:border-transparent transition-all duration-300 resize-none",placeholder:"Tuliskan pesan dan doa untuk kami..."})]}),(0,a.jsx)(sA.button,{whileHover:{scale:1.05},whileTap:{scale:.95},type:"submit",className:"w-full bg-gradient-to-r from-rose-400 to-pink-500 text-white py-4 rounded-xl font-semibold text-lg shadow-lg hover:shadow-xl transition-all duration-300",children:"Kirim RSVP"})]})}),(0,a.jsxs)(sA.div,{variants:r,className:"text-center mt-12",children:[(0,a.jsx)(sA.div,{animate:{rotate:[0,5,-5,0],scale:[1,1.1,1]},transition:{duration:3,repeat:1/0,ease:"easeInOut"},className:"text-4xl mb-4",children:"\uD83D\uDE4F"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Kehadiran dan doa restu Anda merupakan kebahagiaan bagi kami"})]})]})})})}function sN(){let e={hidden:{y:30,opacity:0},visible:{y:0,opacity:1,transition:{duration:.6}}};return(0,a.jsx)("footer",{className:"bg-gradient-to-br from-gray-800 to-gray-900 text-white py-16 px-4",children:(0,a.jsx)("div",{className:"max-w-4xl mx-auto",children:(0,a.jsxs)(sA.div,{initial:"hidden",whileInView:"visible",viewport:{once:!0,amount:.3},variants:{hidden:{opacity:0},visible:{opacity:1,transition:{staggerChildren:.2}}},className:"text-center",children:[(0,a.jsxs)(sA.div,{variants:e,className:"mb-12",children:[(0,a.jsx)(sA.div,{animate:{rotate:[0,10,-10,0],scale:[1,1.2,1]},transition:{duration:4,repeat:1/0,ease:"easeInOut"},className:"text-6xl mb-6",children:"\uD83D\uDC95"}),(0,a.jsx)("h2",{className:"text-3xl md:text-4xl font-bold mb-4",children:"Sarah & Ahmad"}),(0,a.jsx)("p",{className:"text-gray-300 text-lg mb-6",children:"Terima kasih telah menjadi bagian dari hari bahagia kami"})]}),(0,a.jsxs)(sA.div,{variants:e,className:"bg-white bg-opacity-10 backdrop-blur-sm rounded-2xl p-8 mb-12",children:[(0,a.jsx)("h3",{className:"text-2xl font-bold mb-4",children:"Save The Date"}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-center",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-3xl font-bold text-rose-300",children:"15"}),(0,a.jsx)("div",{className:"text-sm text-gray-300",children:"Juli"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-3xl font-bold text-rose-300",children:"2024"}),(0,a.jsx)("div",{className:"text-sm text-gray-300",children:"Tahun"})]})]})]}),(0,a.jsxs)(sA.div,{variants:e,className:"mb-12",children:[(0,a.jsx)("blockquote",{className:"text-lg italic text-gray-300 mb-4",children:"“Cinta adalah komposisi dari satu jiwa yang mendiami dua tubuh”"}),(0,a.jsx)("cite",{className:"text-gray-400",children:"- Aristoteles"})]}),(0,a.jsx)(sA.div,{variants:e,className:"border-t border-gray-600 pt-8",children:(0,a.jsxs)("div",{className:"flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0",children:[(0,a.jsx)("div",{className:"text-gray-400 text-sm",children:"\xa9 2024 Sarah & Ahmad Wedding. Made with ❤️"}),(0,a.jsx)("div",{className:"flex space-x-4 text-sm text-gray-400",children:(0,a.jsx)(sA.button,{whileHover:{scale:1.05},onClick:()=>window.scrollTo({top:0,behavior:"smooth"}),className:"hover:text-white transition-colors duration-300",children:"Kembali ke Atas ↑"})})]})}),(0,a.jsx)(sA.div,{animate:{y:[-10,-20,-10],rotate:[0,5,-5,0]},transition:{duration:4,repeat:1/0,ease:"easeInOut"},className:"absolute bottom-20 left-10 text-2xl opacity-30",children:"\uD83D\uDC96"}),(0,a.jsx)(sA.div,{animate:{y:[-15,-25,-15],rotate:[0,-5,5,0]},transition:{duration:5,repeat:1/0,ease:"easeInOut",delay:1},className:"absolute bottom-32 right-10 text-xl opacity-30",children:"\uD83D\uDC9D"})]})})})}var sD=i(6180),sO=i.n(sD);class sV extends nt.Component{getSnapshotBeforeUpdate(e){let t=this.props.childRef.current;if(t&&e.isPresent&&!this.props.isPresent){let e=t.offsetParent,i=tJ(e)&&e.offsetWidth||0,n=this.props.sizeRef.current;n.height=t.offsetHeight||0,n.width=t.offsetWidth||0,n.top=t.offsetTop,n.left=t.offsetLeft,n.right=i-n.width-n.left}return null}componentDidUpdate(){}render(){return this.props.children}}function sI({children:e,isPresent:t,anchorX:i,root:n}){let r=(0,nt.useId)(),s=(0,nt.useRef)(null),o=(0,nt.useRef)({width:0,height:0,top:0,left:0,right:0}),{nonce:l}=(0,nt.useContext)(rk);return(0,nt.useInsertionEffect)(()=>{let{width:e,height:a,top:u,left:d,right:c}=o.current;if(t||!s.current||!e||!a)return;let h="left"===i?`left: ${d}`:`right: ${c}`;s.current.dataset.motionPopId=r;let p=document.createElement("style");l&&(p.nonce=l);let m=n??document.head;return m.appendChild(p),p.sheet&&p.sheet.insertRule(`
          [data-motion-pop-id="${r}"] {
            position: absolute !important;
            width: ${e}px !important;
            height: ${a}px !important;
            ${h}px !important;
            top: ${u}px !important;
          }
        `),()=>{m.removeChild(p),m.contains(p)&&m.removeChild(p)}},[t]),(0,a.jsx)(sV,{isPresent:t,childRef:s,sizeRef:o,children:nt.cloneElement(e,{ref:s})})}let sF=({children:e,initial:t,isPresent:i,onExitComplete:n,custom:r,presenceAffectsLayout:s,mode:o,anchorX:l,root:u})=>{let d=r5(sL),c=(0,nt.useId)(),h=!0,p=(0,nt.useMemo)(()=>(h=!1,{id:c,initial:t,isPresent:i,custom:r,onExitComplete:e=>{for(let t of(d.set(e,!0),d.values()))if(!t)return;n&&n()},register:e=>(d.set(e,!1),()=>d.delete(e))}),[i,d,n]);return s&&h&&(p={...p}),(0,nt.useMemo)(()=>{d.forEach((e,t)=>d.set(t,!1))},[i]),nt.useEffect(()=>{i||d.size||!n||n()},[i]),"popLayout"===o&&(e=(0,a.jsx)(sI,{isPresent:i,anchorX:l,root:u,children:e})),(0,a.jsx)(ni.Provider,{value:p,children:e})};function sL(){return new Map}let s$=e=>e.key||"";function sB(e){let t=[];return nt.Children.forEach(e,e=>{(0,nt.isValidElement)(e)&&t.push(e)}),t}let sU=({children:e,custom:t,initial:i=!0,onExitComplete:n,presenceAffectsLayout:r=!0,mode:s="sync",propagate:o=!1,anchorX:l="left",root:u})=>{let[d,c]=nn(o),h=(0,nt.useMemo)(()=>sB(e),[e]),p=o&&!d?[]:h.map(s$),m=(0,nt.useRef)(!0),f=(0,nt.useRef)(h),g=r5(()=>new Map),[y,v]=(0,nt.useState)(h),[x,b]=(0,nt.useState)(h);rF(()=>{m.current=!1,f.current=h;for(let e=0;e<x.length;e++){let t=s$(x[e]);p.includes(t)?g.delete(t):!0!==g.get(t)&&g.set(t,!1)}},[x,p.length,p.join("-")]);let w=[];if(h!==y){let e=[...h];for(let t=0;t<x.length;t++){let i=x[t],n=s$(i);p.includes(n)||(e.splice(t,0,i),w.push(i))}return"wait"===s&&w.length&&(e=w),b(sB(e)),v(h),null}let{forceRender:j}=(0,nt.useContext)(nr);return(0,a.jsx)(a.Fragment,{children:x.map(e=>{let y=s$(e),v=(!o||!!d)&&(h===x||p.includes(y));return(0,a.jsx)(sF,{isPresent:v,initial:(!m.current||!!i)&&void 0,custom:t,presenceAffectsLayout:r,mode:s,root:u,onExitComplete:v?void 0:()=>{if(!g.has(y))return;g.set(y,!0);let e=!0;g.forEach(t=>{t||(e=!1)}),e&&(j?.(),b(f.current),o&&c?.(),n&&n())},anchorX:l,children:e},y)})})};function sz({src:e,title:t="Wedding Song",autoPlay:i=!1}){let[n,r]=(0,nt.useState)(!1),[s,o]=(0,nt.useState)(.5),[l,u]=(0,nt.useState)(!1),[d,c]=(0,nt.useState)(0),[h,p]=(0,nt.useState)(0),m=(0,nt.useRef)(null),f=e=>{let t=Math.floor(e/60),i=Math.floor(e%60);return`${t}:${i.toString().padStart(2,"0")}`};return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("audio",{ref:m,src:e,loop:!0,preload:"metadata",className:"jsx-366b27ecce7dff68"}),(0,a.jsxs)(sA.div,{className:"fixed bottom-6 right-6 z-50",initial:{scale:0,opacity:0},animate:{scale:1,opacity:1},transition:{delay:2,duration:.5},children:[(0,a.jsx)(sA.button,{onClick:()=>u(!l),className:"bg-gradient-to-r from-pink-500 to-rose-500 text-white p-4 rounded-full shadow-lg hover:shadow-xl transition-all duration-300",whileHover:{scale:1.1},whileTap:{scale:.95},children:(0,a.jsx)(sA.div,{animate:{rotate:360*!!n},transition:{duration:3,repeat:n?1/0:0,ease:"linear"},children:"\uD83C\uDFB5"})}),(0,a.jsx)(sU,{children:l&&(0,a.jsxs)(sA.div,{initial:{opacity:0,y:20,scale:.8},animate:{opacity:1,y:0,scale:1},exit:{opacity:0,y:20,scale:.8},className:"absolute bottom-16 right-0 bg-white/95 backdrop-blur-sm rounded-2xl p-4 shadow-xl border border-pink-100 min-w-[280px]",children:[(0,a.jsxs)("div",{className:"jsx-366b27ecce7dff68 text-center mb-3",children:[(0,a.jsx)("h4",{className:"jsx-366b27ecce7dff68 font-semibold text-gray-800 text-sm",children:t}),(0,a.jsxs)("p",{className:"jsx-366b27ecce7dff68 text-xs text-gray-500",children:[f(d)," / ",f(h)]})]}),(0,a.jsx)("div",{className:"jsx-366b27ecce7dff68 flex items-center justify-center mb-3",children:(0,a.jsx)(sA.button,{onClick:()=>{let e=m.current;e&&(n?e.pause():e.play().catch(console.error),r(!n))},className:"bg-gradient-to-r from-pink-500 to-rose-500 text-white p-3 rounded-full hover:shadow-lg transition-all duration-300",whileHover:{scale:1.05},whileTap:{scale:.95},children:n?"⏸️":"▶️"})}),(0,a.jsxs)("div",{className:"jsx-366b27ecce7dff68 flex items-center gap-2",children:[(0,a.jsx)("span",{className:"jsx-366b27ecce7dff68 text-sm",children:"\uD83D\uDD0A"}),(0,a.jsx)("input",{type:"range",min:"0",max:"1",step:"0.1",value:s,onChange:e=>{let t=parseFloat(e.target.value);o(t),m.current&&(m.current.volume=t)},className:"jsx-366b27ecce7dff68 flex-1 h-2 bg-pink-200 rounded-lg appearance-none cursor-pointer slider"}),(0,a.jsxs)("span",{className:"jsx-366b27ecce7dff68 text-xs text-gray-500 w-8",children:[Math.round(100*s),"%"]})]}),(0,a.jsx)("button",{onClick:()=>u(!1),className:"jsx-366b27ecce7dff68 absolute top-2 right-2 text-gray-400 hover:text-gray-600 text-sm",children:"✕"})]})})]}),(0,a.jsx)(sO(),{id:"366b27ecce7dff68",children:".slider.jsx-366b27ecce7dff68::-webkit-slider-thumb{-webkit-appearance:none;-moz-appearance:none;-ms-appearance:none;appearance:none;width:16px;height:16px;-webkit-border-radius:50%;-moz-border-radius:50%;border-radius:50%;background:-webkit-linear-gradient(45deg,#ec4899,#f43f5e);background:-moz-linear-gradient(45deg,#ec4899,#f43f5e);background:-o-linear-gradient(45deg,#ec4899,#f43f5e);background:linear-gradient(45deg,#ec4899,#f43f5e);cursor:pointer;-webkit-box-shadow:0 2px 4px rgba(0,0,0,.2);-moz-box-shadow:0 2px 4px rgba(0,0,0,.2);box-shadow:0 2px 4px rgba(0,0,0,.2)}.slider.jsx-366b27ecce7dff68::-moz-range-thumb{width:16px;height:16px;-webkit-border-radius:50%;-moz-border-radius:50%;border-radius:50%;background:-webkit-linear-gradient(45deg,#ec4899,#f43f5e);background:-moz-linear-gradient(45deg,#ec4899,#f43f5e);background:-o-linear-gradient(45deg,#ec4899,#f43f5e);background:linear-gradient(45deg,#ec4899,#f43f5e);cursor:pointer;border:none;-webkit-box-shadow:0 2px 4px rgba(0,0,0,.2);-moz-box-shadow:0 2px 4px rgba(0,0,0,.2);box-shadow:0 2px 4px rgba(0,0,0,.2)}"})]})}var sW=i(1261),sH=i.n(sW);function sq(){let[e,t]=(0,nt.useState)([]),[i,n]=(0,nt.useState)({name:"",message:""}),[r,s]=(0,nt.useState)(!1),o=async e=>{if(e.preventDefault(),!i.name.trim()||!i.message.trim())return;s(!0);let r={id:Date.now().toString(),name:i.name.trim(),message:i.message.trim(),timestamp:new Date,avatar:`https://ui-avatars.com/api/?name=${encodeURIComponent(i.name)}&background=ec4899&color=fff&size=40`};await new Promise(e=>setTimeout(e,1e3)),t(e=>[r,...e]),n({name:"",message:""}),s(!1)},l=e=>{let t=Math.floor((new Date().getTime()-e.getTime())/6e4);if(t<1)return"Baru saja";if(t<60)return`${t} menit yang lalu`;let i=Math.floor(t/60);if(i<24)return`${i} jam yang lalu`;let n=Math.floor(i/24);return`${n} hari yang lalu`},u={hidden:{y:50,opacity:0},visible:{y:0,opacity:1,transition:{duration:.8}}};return(0,a.jsx)("section",{className:"py-20 bg-gradient-to-br from-pink-50 to-rose-100",children:(0,a.jsx)("div",{className:"container mx-auto px-4 max-w-4xl",children:(0,a.jsxs)(sA.div,{variants:{hidden:{opacity:0},visible:{opacity:1,transition:{staggerChildren:.1}}},initial:"hidden",whileInView:"visible",viewport:{once:!0},children:[(0,a.jsxs)(sA.div,{variants:u,className:"text-center mb-12",children:[(0,a.jsx)("h2",{className:"text-4xl font-bold text-gray-800 mb-4",children:"\uD83D\uDC8C Pesan & Doa"}),(0,a.jsx)("p",{className:"text-gray-600 max-w-2xl mx-auto",children:"Tinggalkan pesan dan doa terbaik untuk kami. Setiap kata dari Anda sangat berarti bagi kami."})]}),(0,a.jsx)(sA.div,{variants:u,className:"bg-white rounded-2xl shadow-lg p-6 mb-8",children:(0,a.jsxs)("form",{onSubmit:o,className:"space-y-4",children:[(0,a.jsx)("div",{className:"grid md:grid-cols-2 gap-4",children:(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Nama Anda"}),(0,a.jsx)("input",{type:"text",value:i.name,onChange:e=>n(t=>({...t,name:e.target.value})),className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent transition-all duration-300",placeholder:"Masukkan nama Anda",required:!0})]})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Pesan & Doa"}),(0,a.jsx)("textarea",{value:i.message,onChange:e=>n(t=>({...t,message:e.target.value})),rows:4,className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent transition-all duration-300 resize-none",placeholder:"Tulis pesan dan doa terbaik untuk kami...",required:!0})]}),(0,a.jsx)(sA.button,{type:"submit",disabled:r,whileHover:{scale:1.02},whileTap:{scale:.98},className:"w-full bg-gradient-to-r from-pink-500 to-rose-500 text-white py-3 px-6 rounded-lg font-semibold shadow-lg hover:shadow-xl transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed",children:r?(0,a.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[(0,a.jsx)("div",{className:"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"}),"Mengirim..."]}):"Kirim Pesan"})]})}),(0,a.jsxs)(sA.div,{variants:u,className:"space-y-4",children:[(0,a.jsx)("div",{className:"flex items-center justify-between mb-6",children:(0,a.jsxs)("h3",{className:"text-xl font-semibold text-gray-800",children:["Pesan dari Tamu (",e.length,")"]})}),(0,a.jsx)(sU,{children:0===e.length?(0,a.jsxs)(sA.div,{initial:{opacity:0},animate:{opacity:1},className:"text-center py-12 text-gray-500",children:[(0,a.jsx)("div",{className:"text-4xl mb-4",children:"\uD83D\uDCAD"}),(0,a.jsx)("p",{children:"Belum ada pesan. Jadilah yang pertama memberikan doa!"})]}):e.map((e,t)=>(0,a.jsx)(sA.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:-20},transition:{delay:.1*t},className:"bg-white rounded-xl shadow-md p-6 hover:shadow-lg transition-shadow duration-300",children:(0,a.jsxs)("div",{className:"flex items-start gap-4",children:[(0,a.jsx)(sH(),{src:e.avatar||"",alt:e.name,width:40,height:40,className:"w-10 h-10 rounded-full"}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,a.jsx)("h4",{className:"font-semibold text-gray-800",children:e.name}),(0,a.jsx)("span",{className:"text-sm text-gray-500",children:l(e.timestamp)})]}),(0,a.jsx)("p",{className:"text-gray-700 leading-relaxed",children:e.message})]})]})},e.id))})]})]})})})}function sX(){let[e,t]=(0,nt.useState)(!1);return e?(0,a.jsxs)(sA.div,{initial:{opacity:0},animate:{opacity:1},transition:{duration:1},className:"min-h-screen bg-gradient-to-br from-rose-50 via-white to-pink-50",children:[(0,a.jsx)(sR,{}),(0,a.jsx)(sk,{}),(0,a.jsx)(sC,{}),(0,a.jsx)(s_,{}),(0,a.jsx)(sM,{}),(0,a.jsx)(sq,{}),(0,a.jsx)(sN,{}),(0,a.jsx)(sz,{src:"/music/song.mp3",title:"Wedding Song",autoPlay:!0})]}):(0,a.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-rose-100 via-pink-50 to-rose-200 flex items-center justify-center p-4",children:(0,a.jsx)(sA.div,{initial:{scale:.8,opacity:0},animate:{scale:1,opacity:1},transition:{duration:.8,ease:"easeOut"},className:"bg-white rounded-3xl shadow-2xl p-8 max-w-md w-full text-center",children:(0,a.jsxs)(sA.div,{initial:{y:20,opacity:0},animate:{y:0,opacity:1},transition:{delay:.3,duration:.6},children:[(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsx)(sA.div,{animate:{rotate:[0,5,-5,0]},transition:{duration:2,repeat:1/0,repeatDelay:3},className:"text-6xl mb-4",children:"\uD83D\uDC8C"}),(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-800 mb-2",children:"Undangan Pernikahan"}),(0,a.jsx)("p",{className:"text-gray-600 mb-6",children:"Kami mengundang Anda untuk merayakan hari bahagia kami"})]}),(0,a.jsx)(sA.button,{whileHover:{scale:1.05},whileTap:{scale:.95},onClick:()=>{t(!0)},className:"bg-gradient-to-r from-rose-400 to-pink-500 text-white px-8 py-3 rounded-full font-semibold shadow-lg hover:shadow-xl transition-all duration-300",children:"Buka Undangan"})]})})})}},8947:(e,t,i)=>{Promise.resolve().then(i.bind(i,9769))},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9148:(e,t,i)=>{"use strict";e.exports=i(4041).vendored.contexts.RouterContext},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9513:(e,t,i)=>{"use strict";e.exports=i(4041).vendored.contexts.HeadManagerContext},9551:e=>{"use strict";e.exports=require("url")},9584:()=>{},9769:(e,t,i)=>{"use strict";i.d(t,{default:()=>n});let n=(0,i(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\Program\\\\laragon\\\\www\\\\undangan\\\\undangan-online\\\\src\\\\components\\\\WeddingInvitation.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Program\\laragon\\www\\undangan\\undangan-online\\src\\components\\WeddingInvitation.tsx","default")}};var t=require("../webpack-runtime.js");t.C(e);var i=e=>t(t.s=e),n=t.X(0,[447,145],()=>i(1904));module.exports=n})();