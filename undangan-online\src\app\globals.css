@import "tailwindcss";

:root {
  --background: #faf7f0;
  --foreground: #2c1810;

  /* Dark Gold Theme Variables */
  --cream-bg: #faf7f0;
  --cream-light: #fefcf7;
  --dark-gold: #b8860b;
  --gold: #daa520;
  --light-gold: #f4e4a6;
  --dark-brown: #8b4513;
  --text-dark: #2c1810;
  --text-gold: #b8860b;
  --border-gold: #daa520;
  --shadow-gold: rgba(184, 134, 11, 0.3);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

/* Dark Gold Theme Styles */
.ornament-gold {
  background: linear-gradient(45deg, var(--dark-gold), var(--gold));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.border-gold {
  border-color: var(--border-gold);
}

.bg-gold-gradient {
  background: linear-gradient(135deg, var(--dark-gold), var(--gold), var(--light-gold));
}

.shadow-gold {
  box-shadow: 0 4px 20px var(--shadow-gold);
}

.text-gold {
  color: var(--text-gold);
}

.bg-cream {
  background-color: var(--cream-bg);
}

.bg-cream-light {
  background-color: var(--cream-light);
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}
