'use client';

import { motion } from 'framer-motion';

export default function Hero() {
  return (
    <section className="min-h-screen flex items-center justify-center bg-cream relative overflow-hidden">
      {/* Background Ornaments */}
      <div className="absolute inset-0 opacity-20">
        <div className="absolute top-10 left-10 w-20 h-20 border-2 border-gold rounded-full"></div>
        <div className="absolute top-32 right-20 w-16 h-16 border-2 border-gold rounded-full"></div>
        <div className="absolute bottom-20 left-20 w-24 h-24 border-2 border-gold rounded-full"></div>
        <div className="absolute bottom-32 right-10 w-12 h-12 border-2 border-gold rounded-full"></div>

        {/* Gold Ornamental Elements */}
        <div className="absolute top-1/4 left-1/4 text-6xl text-gold opacity-30">❋</div>
        <div className="absolute top-3/4 right-1/4 text-4xl text-gold opacity-30">✦</div>
        <div className="absolute top-1/2 left-10 text-5xl text-gold opacity-30">❈</div>
        <div className="absolute top-1/3 right-10 text-3xl text-gold opacity-30">✧</div>
      </div>

      <div className="text-center z-10 px-4">
        <motion.div
          initial={{ y: -50, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ duration: 1, ease: "easeOut" }}
        >
          <h1 className="text-4xl md:text-6xl font-bold ornament-gold mb-4">
            Sarah & Ahmad
          </h1>
        </motion.div>

        <motion.div
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ delay: 0.5, duration: 0.8, ease: "easeOut" }}
          className="my-8"
        >
          <div className="text-8xl mb-4 text-gold">💍</div>
        </motion.div>

        <motion.div
          initial={{ y: 50, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 1, duration: 1, ease: "easeOut" }}
        >
          <p className="text-xl md:text-2xl text-dark-brown mb-6">
            Dengan penuh sukacita, kami mengundang Anda
          </p>
          <p className="text-lg md:text-xl text-gold mb-8">
            untuk merayakan pernikahan kami
          </p>
        </motion.div>

        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 1.5, duration: 1 }}
          className="text-lg text-gray-600"
        >
          <p className="mb-2">Sabtu, 15 Juli 2024</p>
          <p>Pukul 09.00 WIB</p>
        </motion.div>

        <motion.div
          initial={{ y: 30, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 2, duration: 0.8 }}
          className="mt-12"
        >
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={() => {
              document.getElementById('couple-info')?.scrollIntoView({ 
                behavior: 'smooth' 
              });
            }}
            className="bg-gradient-to-r from-rose-400 to-pink-500 text-white px-8 py-3 rounded-full font-semibold shadow-lg hover:shadow-xl transition-all duration-300"
          >
            Lihat Detail
          </motion.button>
        </motion.div>
      </div>

      {/* Floating Hearts Animation */}
      <motion.div
        animate={{ 
          y: [-20, -40, -20],
          rotate: [0, 10, -10, 0]
        }}
        transition={{ 
          duration: 4, 
          repeat: Infinity, 
          ease: "easeInOut" 
        }}
        className="absolute top-20 left-1/4 text-4xl opacity-30"
      >
        💖
      </motion.div>
      
      <motion.div
        animate={{ 
          y: [-30, -50, -30],
          rotate: [0, -10, 10, 0]
        }}
        transition={{ 
          duration: 5, 
          repeat: Infinity, 
          ease: "easeInOut",
          delay: 1
        }}
        className="absolute top-40 right-1/4 text-3xl opacity-30"
      >
        💝
      </motion.div>
    </section>
  );
}
